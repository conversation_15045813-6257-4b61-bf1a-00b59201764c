import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { ProductInResponseType } from "../../types/products";
import { castToProductType } from "../../utils/data-management/types-casting/products";
import { PaginationType } from "@/types/pagination";
import { CriteriaType } from "../../types";

export default async function retrieveProducts(
  page: number,
  limit: number,
  searchedProducts: string,
  searchType: string,
  criteria: CriteriaType | ""
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  const queryParams = [];

  if (searchedProducts !== "") queryParams.push(`search=${searchedProducts}`);
  if (criteria !== "") queryParams.push(`sortBy=${criteria}`);

  queryParams.push(`searchBy=${searchType}`);
  try {
    const endpoint = `/products/dashboard?page=${page}&limit=${limit}&${queryParams.join(
      "&"
    )}`;

    const res = await GET(endpoint, headers);

    return {
      data: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse)
      ),
      pagination: res.data.pagination as PaginationType,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveProducts(page, limit, searchedProducts, searchType, criteria)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}

export async function retrieveProduct(productId: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/products/${productId}`;

    const res = await GET(`${endpoint}`, headers);

    return castToProductType(res.data as ProductInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveProduct(productId));

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
