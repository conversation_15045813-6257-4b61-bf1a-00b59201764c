import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { castToProductType } from "../../utils/data-management/types-casting/products";
import { ProductInResponseType } from "../../types/products";

export async function retrieveEditableProduct(
  productSlug: string,
  language?: string
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const url = language
      ? `/products/${productSlug}/dashboard?language=${language}`
      : `/products/${productSlug}/dashboard`;
    const res = await GET(url, headers);

    return castToProductType(res.data as ProductInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveEditableProduct(productSlug, language)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
