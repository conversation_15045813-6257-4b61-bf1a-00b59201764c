import { z } from "zod";

const EnglishSignUpFormSchema = z.object({
  firstName: z.string().min(1, { message: "Name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  email: z
    .string()
    .email("Invalid email format")
    .min(1, { message: "L'adresse e-mail est requise." }),
  password: z.string().min(8, {
    message: "Le mot de passe doit contenir au moins 8 caractères.",
  }),
});

export function getSignUpFormSchema() {
  return EnglishSignUpFormSchema;
}
