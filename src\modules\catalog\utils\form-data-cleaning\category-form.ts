export default function cleanCategoryFormData(formData: FormData): FormData {
  const filteredFormData = new FormData();
  let imageDeleted = false;

  formData.forEach((value, key) => {
    if (key === "deleted-image" && value === "null") {
      imageDeleted = true;
      return;
    }

    //image addition
    if (key === "image" && typeof value === "object") {
      if (value.size !== 0) filteredFormData.append(key, value);
    } else if (typeof value === "string" && value.trim() !== "") {
      if (value === "") filteredFormData.append(key, "null");
      else filteredFormData.append(key, value);
    }
  });

  // If the image was deleted, add an empty image field to filteredFormData
  if (imageDeleted && !filteredFormData.get("image")) {
    filteredFormData.append("image", "null");
  }

  if (formData.has("keywords")) {
    filteredFormData.delete("keywords");
  }
  return filteredFormData;
}
