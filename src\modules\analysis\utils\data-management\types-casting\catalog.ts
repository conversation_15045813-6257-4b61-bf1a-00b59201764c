import {
  CategorySalesInResponseType,
  CategorySalesType,
  ProductSalesInResponseType,
  ProductSalesType,
  ProductsOverviewInResponse,
  ProductsOverview,
} from "@/modules/analysis/types/catalog";

export function castToProductSalesType(
  product: ProductSalesInResponseType
): ProductSalesType {
  return {
    id: product.productId,
    productItemId: product.productItemId,
    name: product.name,
    image: `${process.env.BACKEND_ADDRESS}${product.image}`,
    quantitySold: product.totalOrders,
    currency: product.currency,
    revenue: product.totalRevenue,
    availableStock: product.quantity,
  };
}

export function castToCategoryTypeSalesType(
  category: CategorySalesInResponseType
): CategorySalesType {
  return {
    id: category.id,
    name: category.name,
    currency: category.currency,
    revenue: Number(category.totalRevenue),
  };
}

export function castToProductsOverviewType(
  products: ProductsOverviewInResponse
): ProductsOverview {
  return {
    totalProductsSoldCurrentMonth: products.totalSoldProductsCurrentMonth,
    totalProductsSoldLastMonth: products.totalSoldProductsLastMonth,
  };
}
