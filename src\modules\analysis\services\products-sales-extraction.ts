import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { ProductSalesInResponseType } from "../types/catalog";
import { castToProductSalesType } from "../utils/data-management/types-casting/catalog";
import { PaginationType } from "@/types/pagination";
import { CustomError } from "@/utils/custom-error";

export default async function retrieveProductsSales(
  page: number,
  limit?: number
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/statistics/products/details";

    const res = await GET(
      `${endpoint}?page=${page}&${limit ? `limit=${limit}` : ""}`,
      headers
    );

    return {
      data: (res.data.data as ProductSalesInResponseType[]).map(
        (productInResponse) => castToProductSalesType(productInResponse)
      ),
      pagination: res.data.pagination as PaginationType,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveProductsSales(page));

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    throw new CustomError(
      "Error Occured",
      axiosError.response?.status as number
    );
  }
}
