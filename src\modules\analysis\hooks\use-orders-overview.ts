import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { OrdersOverview } from "../types/orders";
import retrieveOrdersOverview from "../services/orders-overview";

export default function useOrdersOverview() {
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<OrdersOverview>({
    queryKey: ["orders-overview", user],
    queryFn: retrieveOrdersOverview,
    enabled: user !== null,
  });

  return {
    data,
    isLoading,
    isError,
  };
}
