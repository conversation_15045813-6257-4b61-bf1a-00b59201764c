import Text from "@/styles/text-styles";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";

interface Props {
  currency: string;
  promotionCurrencyList: string[];
  onClick: (currency: string) => void;
}

export default function PromotionCurrencyDropDown(props: Props) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [currentCurrency, setCurrentCurrency] = useState<string>("");

  useEffect(() => {
    setCurrentCurrency(props.currency);
    props.onClick(props.currency);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.currency]);

  return (
    <Select
      open={menuIsOpen}
      onOpenChange={(isOpen) => setMenuIsOpen(isOpen)}
      onValueChange={(currency: string) => {
        props.onClick(currency);
        setCurrentCurrency(currency);
        setMenuIsOpen(false);
      }}
    >
      <SelectTrigger className="w-fit cursor-pointer outline-none">
        <Text
          textStyle="TS6"
          className={cn("px-3 py-2 flex items-center space-x-2")}
        >
          {currentCurrency}
        </Text>
      </SelectTrigger>

      <SelectContent>
        {props.promotionCurrencyList.map((currency, idx) => (
          <SelectItem
            key={idx}
            className="py-3 bg-white hover:bg-white text-black cursor-pointer"
            value={currency}
          >
            <Text textStyle="TS7">{currency}</Text>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
