import { POST } from "@/lib/http-methods";
import { UserSignInType } from "@auth/types";
import { AxiosError, AxiosHeaders, AxiosResponse } from "axios";

type AuthResponse = {
  status: number;
  ok: boolean;
  errors?: string;
};

export async function signIn(data: UserSignInType): Promise<AuthResponse> {
  const headers = {} as AxiosHeaders;

  try {
    const res: AxiosResponse = await POST(`/auths/login`, headers, data);

    const tokens = res.data as { access: string; refresh: string };

    localStorage.setItem("access", tokens.access);
    localStorage.setItem("refresh", tokens.refresh);

    return { status: 204, ok: true };
  } catch (error) {
    const axiosError = error as AxiosError;

    const responseStatus = axiosError?.response?.status
      ? axiosError?.response?.status
      : 500;

    return {
      status: responseStatus,
      errors: "",
      ok: false,
    };
  }
}
