import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import retrievePromotions from "../services/promotions-extraction";
import { PromotionType } from "../types/promotions";

export default function usePromotions() {
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<PromotionType[]>({
    queryKey: ["promotions", user],
    queryFn: retrievePromotions,
    enabled: user !== null,
  });

  return {
    promotions: data,
    promotionsAreLoading: isLoading,
    promotionsError: isError,
  };
}
