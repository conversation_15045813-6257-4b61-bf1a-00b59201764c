import {
  DropdownMenuContent,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import Text from "@/styles/text-styles";
import { useEffect, useState } from "react";
import { OrderStatus } from "../types/orders";
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";

interface Props {
  orderId: string;
  status: OrderStatus;
  orderStatusList: OrderStatus[];
  onClick: (orderId: string, status: OrderStatus) => void;
}

export default function OrderStatusDropDown(props: Props) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const t = useTranslations("ordersManagement.orderStatus");
  const [currentStatus, setCurrentStatus] = useState<OrderStatus>("Processing");

  useEffect(() => {
    setCurrentStatus(props.status);
  }, [props.status]);

  return (
    <DropdownMenu
      open={menuIsOpen}
      onOpenChange={(isOpen) => setMenuIsOpen(isOpen)}
    >
      <DropdownMenuTrigger className="w-fit cursor-pointer outline-none">
        <Text
          textStyle="TS6"
          className={cn(
            "px-3 py-2 bg-[#F7F9FC] rounded-full flex items-center space-x-2",
            {
              "bg-[#EAF4FF] text-blue": currentStatus === "Processing",
              "bg-[#FFF3E4] text-[#8F4917]": "Returned" == currentStatus,
              "bg-[#EDFBEE] text-[#13A570]": currentStatus === "Delivered",
              "bg-[#F7F9FC] text-gray": currentStatus === "Cancelled",
              "bg-[#FFCBCB] text-[##F90000]": currentStatus === "Cancelled",
            }
          )}
        >
          <span>{t(currentStatus)}</span>
          <ChevronDown size={20} />
        </Text>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        {props.orderStatusList.map((status, idx) => (
          <DropdownMenuItem
            key={idx}
            className="py-3 bg-white hover:bg-white text-black cursor-pointer"
            onClick={() => {
              props.onClick(props.orderId, status);
              setMenuIsOpen(false);
            }}
          >
            <Text
              textStyle="TS7"
              className={cn({
                " text-blue": status === "Processing",
                "text-[#8F4917]": "Returned" == status,
                "text-[#13A570]": status === "Delivered",
                "text-gray": status === "Cancelled",
                "text-[##F90000]": status === "Failed",
              })}
            >
              {t(status)}
            </Text>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
