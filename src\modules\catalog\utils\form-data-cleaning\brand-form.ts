export default function cleanBrandFormData(
  formData: FormData,
  elementId?: string
): FormData {
  const filteredFormData = new FormData();
  let imageDeleted = false;
  const languages = ["arabic", "french", "english"];

  const metaFieldsToExclude = [
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
    "keywords",
    "seoContent",
  ];

  formData.forEach((value, key) => {
    if (key === "deleted-image" && value === "null") {
      imageDeleted = true;
      return;
    }

    if (key === "defaultImage") {
      return;
    }

    if (metaFieldsToExclude.includes(key)) {
      return;
    }

    //image addition
    if (key === "image" && typeof value === "object") {
      if (value.size !== 0) filteredFormData.append(key, value);
    } else if (!elementId && typeof value === "string" && value.trim() !== "") {
      // brand creation
      filteredFormData.append(key, value);
    } else if (elementId) {
      // brand edition
      filteredFormData.append(key, value);
    }
  });

  // If the image was deleted, add an empty image field to filteredFormData
  if (imageDeleted && !filteredFormData.get("image")) {
    filteredFormData.append("image", "null");
  }

  const content: Array<{
    name: string;
    description: string;
    language: string;
  }> = [];

  languages.forEach((lang) => {
    const nameKey = `name_${lang}`;
    const descKey = `description_${lang}`;

    let nameValue = "";
    let descValue = "";

    if (filteredFormData.has(nameKey)) {
      nameValue = (filteredFormData.get(nameKey) as string)?.trim() || "";
      filteredFormData.delete(nameKey);
    }

    if (filteredFormData.has(descKey)) {
      descValue = (filteredFormData.get(descKey) as string)?.trim() || "";
      filteredFormData.delete(descKey);
    }

    if (nameValue !== "" || descValue !== "") {
      content.push({
        name: nameValue,
        description: descValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });
  if (content.length > 0) {
    filteredFormData.append("content", JSON.stringify(content));
  }

  return filteredFormData;
}
