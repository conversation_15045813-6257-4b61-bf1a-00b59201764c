import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { Order, OrderResponseDataType } from "../types/orders";
import { PaginationType } from "@/types/pagination";
import { GET } from "@/lib/http-methods";
import { castToOrderType } from "../utils/data-management/types-casting/order";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { AxiosError } from "axios";

export async function retreiveOrders(
  page: number,
  searchedOrders?: string,
  limit?: number
): Promise<{
  orders: Order[];
  pagination: PaginationType;
} | null> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };
  try {
    const searchQuery = searchedOrders?.trim() ? `&code=${searchedOrders}` : "";
    const limitParams = limit ? `&limit=${limit}` : "";
    const res = await GET(
      `/orders/dashboard?page=${page}${limitParams}${searchQuery}`,
      header
    );
    return {
      pagination: res.data.pagination as PaginationType,
      orders: (res.data.data as OrderResponseDataType[]).map(
        (orderInResponse) => castToOrderType(orderInResponse)
      ),
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retreiveOrders(page, searchedOrders, limit)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;
      return res;
    }

    return null;
  }
}
