import { useQueryClient } from "@tanstack/react-query";
import { CheckedState } from "@radix-ui/react-checkbox";
import { useRef, useState } from "react";
import { useTranslations } from "next-intl";
import { CustomError } from "@/utils/custom-error";
import validateDiscountInput from "../../../catalog/validation/discount-input-validation";
import { DiscountType } from "../../types";
import { PromotionProductItemType } from "../../types/products";
import uploadPromotionDiscountToServerSide, {
  UploadedPromotionDiscountParams,
} from "../../services/products/promotion-product-upload";

export default function useDiscountUpload(
  promotionId: string,
  onSuccess: () => void
) {
  const [discountType, setDiscountType] = useState<DiscountType>("percentage");
  const queryClient = useQueryClient();
  const [currency, setCurrency] = useState("");
  const formRef = useRef<HTMLFormElement>(null);
  const [promotedProductItems, setPromotedProductItems] = useState<
    PromotionProductItemType[]
  >([]);
  const [promotionApplicationType, setPromotionApplicationType] = useState<
    "byProducts" | "byCategories"
  >("byProducts");
  const [warning, setWarning] = useState("");
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);

  const updatePromotion = async (categoriesIds: string[]) => {
    setIsPending(true);

    try {
      const discount = validateDiscountInput(
        "discount",
        discountType
      ) as number;

      if (discountType === "amount")
        promotedProductItems.forEach((productItem) => {
          if (discount > productItem.prices[0].realPrice)
            throw new CustomError("discountMoreThanPrice", 400);
        });

      const uploadedDiscount: UploadedPromotionDiscountParams = {
        promotionId,
        type: discountType,
        discount: discountType === "percentage" ? discount / 100 : discount,
      };

      if (promotionApplicationType === "byCategories")
        uploadedDiscount.categoryIds = categoriesIds;
      else
        uploadedDiscount.productItemIds = promotedProductItems.map(
          (item) => item.id
        );

      await uploadPromotionDiscountToServerSide(uploadedDiscount);

      if (warning !== "") setWarning("");

      onSuccess();
      setPromotedProductItems([]);

      queryClient.invalidateQueries({
        queryKey: ["promotion-products"],
        exact: false,
      });
    } catch (throwedError) {
      const error = throwedError as CustomError;

      if (error?.status === 400) {
        if (error?.code === "P4502") {
          setWarning(
            t("upload.promotions.someProductsRelatedWithAnotherPromotion")
          );
        } else if (error?.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
        } else if (error?.message === "discountMoreThanPrice") {
          setWarning(t("upload.promotions.discountMoreThanPrice"));
        } else {
          setWarning(t("upload.invalidFormat"));
        }
      } else {
        setWarning(t("serverError"));
      }
    }

    setIsPending(false);
  };

  const handleProductCheck = (
    checked: CheckedState,
    productItem: PromotionProductItemType
  ) => {
    if (checked) {
      setPromotedProductItems([...promotedProductItems, productItem]);
    } else {
      const filteredProductItems = promotedProductItems.filter(
        (checkedProductItemId) => checkedProductItemId !== productItem
      );
      setPromotedProductItems(filteredProductItems);
    }
  };

  return {
    isPending,
    warning,
    updatePromotion,
    handleProductCheck,
    promotedProductItems,
    formRef,
    setCurrency,
    currency,
    discountType,
    setDiscountType,
    promotionApplicationType,
    setPromotionApplicationType,
    setPromotedProductItems,
  };
}
