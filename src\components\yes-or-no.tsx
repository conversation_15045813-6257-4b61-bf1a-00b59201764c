import Text from "@/styles/text-styles";
import { useEffect, useState } from "react";
import { Checkbox } from "./ui/checkbox";

interface Props {
  label: string;
  name: string;
  defaultChecked?: boolean;
}
export default function YesOrNo({ label, name, defaultChecked }: Props) {
  const [value, setValue] = useState(false);

  const handleChange = (checked: boolean) => {
    if (checked) setValue(true);
    else setValue(false);
  };

  useEffect(() => {
    if (defaultChecked !== undefined) setValue(defaultChecked);
  }, [defaultChecked]);

  return (
    <div>
      <label className="flex items-center space-x-2 w-fit">
        <Checkbox
          name={name}
          checked={value}
          onCheckedChange={handleChange}
          className="text-blue outline-none bg-white border z-50 w-5 h-5"
        />
        {value ? null : (
          <input className="hidden" name={name} value={"false"} readOnly />
        )}

        <Text textStyle="TS5">{label}</Text>
      </label>
    </div>
  );
}
