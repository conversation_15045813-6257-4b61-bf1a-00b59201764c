import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { CatalogElementType } from "../types";

export async function retrieveEditableElement(
  elementId: string,
  type: CatalogElementType
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/${
      type === "category"
        ? "categories"
        : type === "product"
        ? "products"
        : type === "brand"
        ? "brands"
        : "products/item"
    }/${elementId}`;

    const res = await GET(`${endpoint}`, headers);
    return res.data as { [key: string]: string | any };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveEditableElement(elementId, type)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
