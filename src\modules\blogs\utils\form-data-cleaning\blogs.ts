export default function cleanBlogFormData(
  formData: FormData,
  elementId?: string
): FormData {
  const filteredFormData = new FormData();

  formData.forEach((value, key) => {
    if (key === "id") {
      filteredFormData.append(key, value);
      return;
    }

    if (typeof value === "string" && value.startsWith("blob:")) {
      return;
    }

    if (key === "image") {
      if (value instanceof File && value.size > 0) {
        filteredFormData.append(key, value);
      }
      return;
    }

    if (!elementId) {
      if (
        (typeof value === "string" && value.trim() !== "") ||
        typeof value === "number" ||
        value instanceof File
      ) {
        filteredFormData.append(key, value);
      }
    } else if (!(typeof value === "string" && value.trim() === "")) {
      filteredFormData.append(key, value);
    }
  });

  if (formData.has("keywords")) {
    filteredFormData.delete("keywords");
  }

  return filteredFormData;
}
