import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface CategoryType {
  name: string;
  image: string | null;
  id: string;
  slug: string;
  description?: string | null;
  subCategories: CategoryType[];
  metaContent: SeoMetaContentType | null;
  displayOrder: number;
}

export interface CategorySelectionType extends CategoryType {
  selected: boolean;
}

export interface CategoryInResponseType {
  id: string;
  slug: string;
  name: string;
  description?: string | null;
  image: string | null;
  createdAt?: string;
  subCategories: CategoryInResponseType[];
  metaContent: SeoMetaContentTypeInResponse | null;
  displayOrder: number;
}

//used in filter page
export interface CategorySelectionType {
  name: string;
  image: string | null;
  id: string;
  slug: string;
  selected: boolean;
  subCategories: CategorySelectionType[];
}
