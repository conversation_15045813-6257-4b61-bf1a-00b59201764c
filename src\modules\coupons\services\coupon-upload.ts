import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import { ErrorDataResponse } from "../types/coupons";

export default async function uploadCouponToServerSide(
  jsonObject: Record<string, any>,
  elementId?: string
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!elementId) {
      const endpoint = "/voucher-codes/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        jsonObject
      );

      return res.data;
    } else {
      const endpoint = "/voucher-codes";

      await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}/${elementId}`,
        header,
        jsonObject
      );
    }

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError<ErrorDataResponse>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadCouponToServerSide(jsonObject, elementId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else if (axiosError.response?.status === 404) {
      throw new CustomError("This category is not found!", 404, "P5000");
    } else if (axiosError.response?.status === 400) {
      throw new CustomError(
        axiosError.response?.data?.message,
        400,
        axiosError.response?.data?.code
      );
    }
    throw new CustomError("Server Error!", 500);
  }
}
