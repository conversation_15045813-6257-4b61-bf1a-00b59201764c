import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { retreiveShippingsDetails } from "../services/shippings-extraction";
import { CustomError } from "@/utils/custom-error";
import { useRouter } from "next/navigation";
import { ShippingDetails } from "../types/shipping-cost";

export default function useShippingsDetails() {
  const { user } = useUser();
  const router = useRouter();
  const { data, isLoading, error } = useQuery<unknown, CustomError>({
    queryKey: ["shippings-details"],
    queryFn: () => retreiveShippingsDetails(),
  });

  if (error?.status === 401) router.push("/");
  else if (error?.status === 500)
    return {
      shippings: [],
      areLoading: false,
    };

  return {
    shippings: data as ShippingDetails[],
    areLoading: isLoading,
  };
}
