export interface CategorySalesType {
  id: string;
  name: string;
  revenue: number;
  currency: string;
}

export interface CategoryTypeSalesType {
  id: string;
  name: string;
  revenue: number;
  currency: string;
}

export interface CategorySalesInResponseType {
  id: string;
  name: string;
  totalRevenue: number;
  currency: string;
}

export interface CategoryTypeSalesInResponseType {
  id: string;
  name: string;
  totalRevenue: number;
  currency: string;
}

export interface ProductSalesType {
  id: string;
  productItemId: string;
  name: string;
  image: string;
  quantitySold: number;
  revenue: number;
  availableStock: number;
  currency: string;
}

export interface ProductSalesInResponseType {
  productId: string;
  productItemId: string;
  name: string;
  image: string;
  quantity: number;
  totalOrders: number;
  totalRevenue: number;
  currency: string;
}

export interface ProductsOverviewInResponse {
  totalSoldProductsCurrentMonth: number;
  totalSoldProductsLastMonth: number;
}

export interface ProductsOverview {
  totalProductsSoldCurrentMonth: number;
  totalProductsSoldLastMonth: number;
}
