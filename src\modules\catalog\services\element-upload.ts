import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { CatalogElementType } from "../types";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

export default async function uploadElementToServerSide(
  elementData: FormData,
  type: CatalogElementType,
  elementId?: string
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!elementId) {
      const endpoint =
        type === "product"
          ? "/products/register"
          : type === "category"
          ? "/categories/register"
          : type === "brand"
          ? "/brands/register"
          : "/products/item/register";
      let res;
      if (type === "product") {
        res = await POST(
          `${process.env.BACKEND_ADDRESS}${endpoint}`,
          header,
          elementData
        );
      } else {
        res = await POST(
          `${process.env.BACKEND_ADDRESS}${endpoint}`,
          header,
          elementData
        );
      }

      return res.data;
    } else {
      const endpoint =
        type === "product"
          ? "/products"
          : type === "category"
          ? "/categories"
          : type === "brand"
          ? "/brands"
          : "/products/item";
      if (type === "product") {
        await PATCH(
          `${process.env.BACKEND_ADDRESS}${endpoint}/${elementId}`,
          header,
          elementData
        );
      } else {
        await PATCH(
          `${process.env.BACKEND_ADDRESS}${endpoint}/${elementId}`,
          header,
          elementData
        );
      }
    }

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadElementToServerSide(elementData, type, elementId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
