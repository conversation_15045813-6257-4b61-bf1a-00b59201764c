# Module Setup Guide

This guide will help you set up auth module into your project

## 1. Cloning Auth application

## 2. deletion of .git file to prevent git conflicts and start managing only a single repository

## 3. Setting Up Workspaces in `package.json`

To enable workspaces in your project, define the `workspaces` field in your root `package.json`. This allows npm to manage each module as part of a monorepo setup.

1. Open your project’s root `package.json` file.
2. Add or update the `workspaces` field to include paths to your modules. For example:

   ```json
   {
     "name": "my-app",
     "version": "1.0.0",
     "private": true,
     "workspaces": [
       "src/modules/*" // Adjust the path as needed
     ],
     "scripts": {
       "dev": "next dev",
       "build": "next build",
       "start": "next start",
       "lint": "next lint"
     }
   }
   ```

3. module packages installations
   ```bash
   npm i --workspace @tawerdigitalservices/auth
   ```

## 4. Installing TanStack Across the App

Ensure TanStack is installed across your application to utilize its features.

```bash
npm install @tanstack/react-query
```

## 5. http file should exist to use exported functionnalities in http request

Ensure that an HTTP file exists to use the exported functionalities for HTTP requests. Create a file named `http.js` or `http.ts` in the appropriate directory, depending on your project setup.

## 6. set up Locales type under @/types

Create a types file for handling locales under the `@/types` directory. This will help manage localization in your application.

## 7. set up warn-input component to use it in the auth

Implement the Warn-Input component to use it in the Auth module.

## 8. Adding Environment Variables

To configure your application, you need to add the following environment variables:

1. **BACKEND_ADDRESS**: The backend address for your application.

2. **PRIVATE_ACCESS_BACKEND_ADDRESS**: Set to `test.tawer.tn`, this variable is used to restrict access to secure client work.

3. **FRONTEND_DOMAIN_NAME**: The current frontend domain name for external authentication redirection and cookie management.

4. **GOOGLE_CLIENT_ID**: The client ID for Google authentication.

5. **FACEBOOK_CLIENT_ID**: The client ID for Facebook authentication.
