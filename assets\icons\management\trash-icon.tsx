interface Props {
  color?: string;
  type?: "regular" | "solid";
}

export default function TrashIcon({ color = "white", type = "solid" }: Props) {
  return type === "solid" ? (
    <svg
      width="19"
      height="21"
      viewBox="0 0 19 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.8889 8.55371C15.8889 16.5728 17.0432 20.1975 9.27942 20.1975C1.51466 20.1975 2.69276 16.5728 2.69276 8.55371"
        fill={color}
      />
      <path
        d="M17.3653 5.47961H1.21484"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.7158 5.47958C12.7158 5.47958 13.2444 1.71387 9.29009 1.71387C5.33676 1.71387 5.86533 5.47958 5.86533 5.47958"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ) : (
    <svg
      width="14"
      height="18"
      viewBox="0 0 14 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13 5V16C13 17.1046 12.1046 18 11 18H3C1.89543 18 1 17.1046 1 16V5H13ZM11 7H3V16H11V7ZM7 0C7.55228 0 8 0.447715 8 1V2H13C13.5523 2 14 2.44772 14 3C14 3.55228 13.5523 4 13 4H1C0.447715 4 0 3.55228 0 3C0 2.44772 0.447715 2 1 2H6V1C6 0.447715 6.44772 0 7 0Z"
        fill={color}
      />
    </svg>
  );
}
