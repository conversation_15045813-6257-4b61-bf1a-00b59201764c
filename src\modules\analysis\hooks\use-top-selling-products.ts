import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { ProductType } from "../../catalog/types/products";
import { useEffect, useState } from "react";
import retrieveTopSellingProducts from "../services/top-selling-products-extraction";
import { PaginationType } from "@/types/pagination";

export default function useTopSellingProducts(productsNumbers: number) {
  const { user } = useUser();
  const [currentPage, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { data, isLoading, isError } = useQuery<{
    data: ProductType[];
    pagination: PaginationType;
  }>({
    queryKey: ["products", user, currentPage],
    queryFn: () => retrieveTopSellingProducts(currentPage, productsNumbers),
    enabled: user !== null,
  });

  useEffect(() => {
    if (data && data.pagination) {
      setTotalPages(data.pagination.totalPages);
    }
  }, [data]);

  return {
    products: data?.data,
    productsAreLoading: isLoading,
    productsError: isError,
    changePage: setPage,
    currentPage,
    totalPages,
  };
}
