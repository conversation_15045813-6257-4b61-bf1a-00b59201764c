"use client";
import { useTranslations } from "next-intl";
import { InfiniteMovingCards } from "../ui/infinite-moving-cards";

export default function Band() {
  const t = useTranslations("welcoming");
  return (
    <div className="relative w-full">
      <div className="bg-[#8A5FFF] relative z-50 L:h-[131px] h-[65px] w-[calc(100%+15px)] -mt-2 -ml-2  px-[73px] rotate-[-3.90deg] flex items-center space-x-[70px] text-white">
        <InfiniteMovingCards
          items={t.raw("services")}
          direction="right"
          speed="slow"
        />
      </div>
      <div className="z-10 bg-[#C4AFFF] absolute -left-1 right-0 L:bottom-[65px] bottom-3  L:h-[90px] h-[60px]" />
    </div>
  );
}
