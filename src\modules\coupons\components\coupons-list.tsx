"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import useCoupons from "../hooks/use-coupons";
import CouponsTable from "./coupon-table";
import TrashIcon from "@assets/icons/management/trash-icon";
import useElementsDeletion from "@/modules/catalog/hooks/use-elements-deletion";
import PaginationMangement from "@/components/pagination/pagination-management";
import DashboardCouponsListsContainer, {
  DashboardCouponsListsContainerSkeleton,
} from "@/components/dashbord-coupons-lists-container";
import StatusButtons from "./status-buttons";
import CouponsSearchbar from "@/components/coupons-searchbar";

export default function CouponsList() {
  const {
    coupons,
    couponsAreLoading,
    setPage,
    pagesNumber,
    page,
    searchedCoupons,
    setSearchedCoupons,
    selectedStatus,
    setSelectedStatus,
    records,
  } = useCoupons(10);

  const t = useTranslations("CouponsManagement");

  const {
    isPending,
    deleteAllElements,
    alertModalIsOpen,
    cancelDeletion,
    onDelete,
    setElementsIds,
  } = useElementsDeletion("coupon");

  const handleStatusSelect = (value: string) => {
    setSelectedStatus(value);
  };

  return !(couponsAreLoading || coupons === undefined) ? (
    <DashboardCouponsListsContainer
      title={t("title")}
      headerElement={
        <div className="w-full">
          <div className="flex space-x-4">
            <StatusButtons
              options={t.raw("couponStatusOptions")}
              onSelect={handleStatusSelect}
              selectedValue={selectedStatus}
            />
          </div>
          <hr className="mb-4" />
          <div className="flex justify-between items-center">
            <div className="flex space-x-5">
              <CouponsSearchbar
                searchedContent={searchedCoupons}
                setSearchedContent={setSearchedCoupons}
              />
            </div>
            <div className="flex space-x-5">
              {coupons && coupons.length > 0 && (
                <div className="w-full flex justify-end space-x-2">
                  <button
                    onClick={onDelete}
                    className="p-2 rounded-md border hover:border-red transition-colors duration"
                    id="delete-coupon-button"
                  >
                    <TrashIcon color="blue" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      }
    >
      <div className="flex flex-col space-y-3">
        {coupons && coupons.length > 0 ? (
          <CouponsTable coupons={coupons} setElementsIds={setElementsIds} />
        ) : (
          <NoDataFound className="py-5 flex justify-center items-center" />
        )}
        {pagesNumber > 1 && (
          <div className="flex justify-center pt-3 px-3">
            <PaginationMangement
              records={records}
              currentPage={page}
              pagesNumber={pagesNumber}
              changePage={setPage}
            />
          </div>
        )}
        <AlertDialog open={alertModalIsOpen}>
          <ModalDialog
            title={t("dialog.title")}
            details={t("dialog.details")}
            cancel={t("dialog.cancel")}
            confirm={t("dialog.confirm")}
            isPending={isPending}
            onCancel={cancelDeletion}
            onConfirm={deleteAllElements}
            theme="red"
          />
        </AlertDialog>
      </div>
    </DashboardCouponsListsContainer>
  ) : (
    <DashboardCouponsListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardCouponsListsContainerSkeleton>
  );
}
