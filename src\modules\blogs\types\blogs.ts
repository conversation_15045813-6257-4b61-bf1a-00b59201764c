import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface BlogType {
  id: string;

  title: string;
  slug: string;
  description: string;
  details: string;

  metaContent?: SeoMetaContentType | null;

  displayOrder: number;

  image: string | null;
}

export interface BlogInResponseType {
  id: string;

  title: string;
  slug: string;
  description: string;
  details: string;

  metaContent?: SeoMetaContentTypeInResponse | null;

  displayOrder: number;

  image: string;
}
