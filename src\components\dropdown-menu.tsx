import { cn } from "@/lib/utils";
import ChevronIcon from "@assets/icons/chevron";
import { useClickAway } from "@uidotdev/usehooks";
import { Dispatch, HTMLAttributes, LegacyRef, SetStateAction } from "react";

interface DropdownMenuItemProps extends HTMLAttributes<HTMLDivElement> {
  isChecked?: boolean;
  last?: boolean;
}

interface DropdownMenuTriggerProps extends HTMLAttributes<HTMLDivElement> {
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  isOpen: boolean;
}

interface DropdownMenuContentProps extends HTMLAttributes<HTMLDivElement> {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}

export function DropdownMenu(props: HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("relative flex-1", props.className)}>
      {props.children}
    </div>
  );
}

export function DropdownMenuTrigger({
  children,
  setIsOpen,
  isOpen,
  className,
}: DropdownMenuTriggerProps) {
  return (
    <div
      onClick={() => setIsOpen(!isOpen)}
      className={cn(
        "flex-1 XL:h-[60px] L:h-[50px] h-10 px-5 rounded-md flex items-center justify-between  bg-opacity-55",
        className
      )}
    >
      {children}
      <ChevronIcon color="white" />
    </div>
  );
}
export function DropDownMenuContent({
  setIsOpen,
  isOpen,
  children,
  className,
}: DropdownMenuContentProps) {
  const contentRef = useClickAway(() => setIsOpen(false));

  return (
    <div
      ref={contentRef as LegacyRef<HTMLDivElement>}
      className={cn(
        "z-50 bg-primary w-full border-primary rounded-md absolute top-[calc(100%+8px)] flex flex-col max-h-40 overflow-y-auto dropdown-scrollbar ",
        className,
        {
          "max-h-0": !isOpen,
          border: isOpen,
        }
      )}
    >
      {children}
    </div>
  );
}

export function DropDownMenuItem({
  isChecked = false,
  last = false,
  children,
  className,
  ...props
}: DropdownMenuItemProps) {
  return (
    <div className="bg-black flex-1">
      <div
        {...props}
        className={cn(
          "w-full py-4 px-3 h-fit bg-white hover:bg-opacity-100 bg-opacity-55 font-tajawal text-white border-white",
          className,
          { "bg-opacity-100": isChecked, "border-b": !last }
        )}
      >
        {children}
      </div>
    </div>
  );
}
