"use client";
import Dashboard<PERSON>ists<PERSON>ontainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { useTranslations } from "next-intl";
import useUserOrders from "../hooks/use-orders";
import BagIcon from "@assets/icons/bag-icon";
import Text from "@/styles/text-styles";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";

export default function OrdersInfoList() {
  const t = useTranslations("dashboard.orders");
  const { orders, ordersAreLoading } = useUserOrders(8);
  const router = useRouter();

  return !(ordersAreLoading || orders === undefined) ? (
    <DashboardListsContainer
      title={t("title")}
      headerElement={
        <button onClick={() => router.push("/orders")} id="see-more-button">
          <Text textStyle="TS5" className="text-blue">
            {t("seeMore")}
          </Text>
        </button>
      }
    >
      {orders && orders.length > 0 ? (
        <Table>
          <TableBody className="text-[#6F7182]">
            {orders.map((order, idx) => (
              <TableRow
                key={order.id}
                className="flex items-center justify-between"
              >
                <TableCell className="font-medium flex space-x-2 items-center">
                  <BagIcon />
                  <div className="flex flex-col">
                    <Text textStyle="TS6" className="font-bold text-gray">
                      Order {order.code}
                    </Text>
                    <Text textStyle="TS6" className="">
                      {`${order.total.toFixed(3)} ${order.currency}`}
                    </Text>
                  </div>
                </TableCell>
                <TableCell className="flex flex-col">{`${order.address.firstName} ${order.address.lastName}`}</TableCell>

                <TableCell>
                  <Text
                    textStyle="TS6"
                    className={cn("px-3 py-2 bg-[#F7F9FC] rounded-full", {
                      "bg-[#EAF4FF] text-blue": order.status === "Processing",
                      "bg-[#FFF3E4] text-[#8F4917]": "Returned" == order.status,
                      "bg-[#EDFBEE] text-[#13A570]":
                        order.status === "Delivered",
                      "bg-[#F7F9FC] text-gray": order.status === "Cancelled",
                      "bg-[#FFCBCB] text-[##F90000]":
                        order.status === "Cancelled",
                    })}
                  >
                    {order.status}
                  </Text>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <NoDataFound className="min-h-40 flex justify-center items-center" />
      )}
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 5 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
