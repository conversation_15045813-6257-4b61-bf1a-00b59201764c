import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

export default async function uploadBlogToServerSide(
  elementData: FormData,
  elementId?: string
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!elementId) {
      const endpoint = "/blogs/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        elementData
      );

      return res.data;
    } else {
      const endpoint = "/blogs";

      await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}/${elementId}`,
        header,
        elementData
      );
    }

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadBlogToServerSide(elementData, elementId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else if (axiosError.response?.status === 400) {
      const errorData = axiosError.response.data as {
        message: string;
        code: string;
      };
      if (errorData.code === "P5000") {
        throw new CustomError("This category is not found.", 400, "P5000");
      } else if (errorData.code === "P9501") {
        throw new CustomError(
          "A blog with this name already exists!",
          400,
          "P9501"
        );
      } else {
        throw new CustomError("Bad Request", 400);
      }
    } else if (axiosError.response?.status === 404) {
      const errorData = axiosError.response.data as {
        message: string;
        code: string;
      };
      if (errorData.code === "P9000") {
        throw new CustomError(
          "This tag does not exist in the portfolio!",
          404,
          "P9000"
        );
      } else {
        throw new CustomError("Not Found", 404);
      }
    }
    throw new CustomError("Server Error!", 500);
  }
}
