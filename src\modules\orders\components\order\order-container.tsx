"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Order } from "../../types/orders";
import OrderStatusDropDown from "../status-drop-down";
import useOrderStatusEdition from "../../hooks/use-order-status-edition";
import { ScrollArea } from "@/components/ui/scroll-area";
import OrderItemContainer from "./order-item-container";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { Separator } from "@/components/ui/separator";

type Props = {
  order: Order;
};

export default function OrderContainer({ order }: Props) {
  const t = useTranslations("ordersManagement.orderInfo");
  const { currency } = useCurrency();

  const { isPending, updateOrderStatus, orderStatusList, warning } =
    useOrderStatusEdition(() => {});

  return (
    <ScrollArea className="max-w-[400px]">
      <div className="flex flex-col justify-between space-y-4 rounded-2xl border border-lightGray p-4 bg-white">
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("number")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {order.code}
          </Text>
        </div>
        <div>
          <Text textStyle="TS6" className="font-bold block">
            {t("items")}
          </Text>
          {order.items.map((item, idx) => (
            <OrderItemContainer key={idx} item={item} />
          ))}
        </div>

        <div className="flex flex-col space-y-3">
          <div className="flex flex-col gap-1">
            <Text textStyle="TS6" className="font-bold text-black">
              {order.couponCodeDiscount !== 0
                ? t("labels.subTotalWithoutDiscount")
                : t("labels.subTotal")}
            </Text>
            <Text textStyle="TS6">{`${order.total.toFixed(
              3
            )} ${currency}`}</Text>
          </div>

          {order.couponCodeDiscount !== 0 && (
            <div className="flex flex-col gap-1">
              <Text textStyle="TS6" className="font-bold text-black">
                {t("labels.couponCode")}
              </Text>
              <Text textStyle="TS6">{`${order.couponCodeDiscount.toFixed(
                3
              )} ${currency}`}</Text>
            </div>
          )}

          <div className="flex flex-col gap-1">
            <Text textStyle="TS6" className="font-bold text-black">
              {t("labels.shippingCost")}
            </Text>
            <Text textStyle="TS6">{`${order.shippingCost.toFixed(
              3
            )} ${currency}`}</Text>
          </div>

          <Separator />

          <div className="flex flex-col gap-1">
            <Text textStyle="TS6" className="font-bold text-black">
              {order.couponCodeDiscount !== 0
                ? t("labels.subTotalWithDiscount")
                : t("labels.total")}
              :
            </Text>
            <Text textStyle="TS6">
              {`${(
                order.total +
                order.shippingCost -
                order.couponCodeDiscount
              ).toFixed(3)} ${currency}`}
            </Text>
          </div>
        </div>
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("details")}
          </Text>
          <div className="flex flex-col gap-2">
            <Text textStyle="TS6" className="mt-2">
              {`${order.address.firstName} ${order.address.lastName}`}
            </Text>
            {order.address.company ? (
              <Text textStyle="TS6">{order.address.company}</Text>
            ) : null}
            <Text textStyle="TS6">{order.address.phone}</Text>
            <Text textStyle="TS6">{`${order.address.address1} ${
              order.address.city.country.name
            } ${order.address.postalCode ? order.address.postalCode : ""} ${
              order.address.city.name
            }`}</Text>
            {order.address.address2 ? (
              <Text textStyle="TS6">{order.address.address2}</Text>
            ) : null}
          </div>
        </div>
        <div className="flex flex-col space-y-3">
          <Text textStyle="TS6" className="font-bold block">
            {t("status")}
          </Text>
          <OrderStatusDropDown
            orderId={order.id}
            status={order.status}
            orderStatusList={orderStatusList}
            onClick={updateOrderStatus}
          />
        </div>
      </div>
    </ScrollArea>
  );
}
