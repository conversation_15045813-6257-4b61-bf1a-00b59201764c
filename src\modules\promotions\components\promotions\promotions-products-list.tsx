"use client";
import { useTranslations } from "next-intl";
import PaginationMangement from "@/components/pagination/pagination-management";
import { useState } from "react";
import { DiscountUpload } from "./discount-upload";
import PromotionProductContainer from "./promotion-product-container";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import useDiscountUpload from "../../hooks/products/use-discount-upload";
import usePromotionProducts from "../../hooks/products/use-promotion-products";
import usePromotionProductsDeletion from "../../hooks/products/use-promotion-products-deletion";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import StatusButtons from "@/modules/coupons/components/status-buttons";
import CouponsSearchbar from "@/components/coupons-searchbar";
import Text from "@/styles/text-styles";
import { cn } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import usePromotion from "../../hooks/use-promotion";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckedState } from "@radix-ui/react-checkbox";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import CategoriesSelection from "@/modules/catalog/components/categories/categories-selection";

interface PromotionsProductsProps {
  slug: string;
}

export default function PromotionsProductsList({
  slug,
}: PromotionsProductsProps) {
  const { promotion } = usePromotion(slug);
  const t = useTranslations("PromotionsManagement");
  const [uploadModalIsOpen, setUploadModalIsOpen] = useState(false);
  const buttonsContent = useTranslations("shared");
  const {
    records,
    totalPages,
    currentPage,
    changePage,
    products,
    productsAreLoading,
    searchedProducts,
    setSearchedProducts,
    displayedProductsType,
    setDisplayedProductsType,
    displayedFilter,
    setDisplayedFilter,
    setSelectedCategories,
    selectedCategories,
  } = usePromotionProducts({ limit: 20, slug });

  const {
    handleProductCheck,
    setCurrency,
    promotedProductItems,
    updatePromotion,
    isPending,
    warning,
    currency,
    discountType,
    setDiscountType,
    promotionApplicationType,
    setPromotionApplicationType,
    setPromotedProductItems,
  } = useDiscountUpload(promotion ? promotion.id : "", () => {
    setUploadModalIsOpen(false);
  });
  const {
    isPending: deletionIsPending,
    alertModalIsOpen,
    cancelDeletion,
    warning: deletionWarning,
    deletePromotionProducts,
    requestPromotionProductsDeletion,
    requestPromotionProductDeletion,
    handleProductDeletionCheck,
    productItemIdsToDelete,
  } = usePromotionProductsDeletion(promotion ? promotion.id : "");

  const handleStatusSelect = (value: string) => {
    setDisplayedFilter(value);
    if (value === "Avec réduction") {
      setDisplayedProductsType("promoted");

      if (promotionApplicationType !== "byProducts")
        setPromotionApplicationType("byProducts");
    } else {
      setDisplayedProductsType("unpromoted");
    }
  };

  const openDiscountUploadPopUp = () => {
    if (promotionApplicationType === "byProducts")
      if (promotedProductItems.length === 0)
        toast({
          title: t("unpassedProductsForDiscountWarning.title"),
          description: t("unpassedProductsForDiscountWarning.description"),
        });
      else setUploadModalIsOpen(true);
    else {
      if (selectedCategories?.length === 0)
        toast({
          title: t("unpassedCategoriesForDiscountWarning.title"),
          description: t("unpassedCategoriesForDiscountWarning.description"),
        });
      else setUploadModalIsOpen(true);
    }
  };

  const openProductsDeletionPopUp = () => {
    if (productItemIdsToDelete.length === 0)
      toast({
        title: t("unpassedProductsForDiscountDeletionWarning.title"),
        description: t(
          "unpassedProductsForDiscountDeletionWarning.description"
        ),
      });
    else requestPromotionProductsDeletion();
  };

  const selectAllProducts = (checked: CheckedState) => {
    if (checked && products)
      setPromotedProductItems(products.flatMap((product) => product.items));
    else setPromotedProductItems([]);
  };

  return !(productsAreLoading || products === undefined) ? (
    <DashboardListsContainer
      headerElement={
        <div className="w-full">
          <div className="flex space-x-4">
            <StatusButtons
              options={t.raw("productsManagement.filters.labels")}
              onSelect={handleStatusSelect}
              selectedValue={displayedFilter}
            />
          </div>
          <hr className="mb-4" />
          <div className="flex flex-col regularL:flex-row justify-between gap-4">
            <CouponsSearchbar
              searchedContent={searchedProducts}
              setSearchedContent={setSearchedProducts}
            />
            <div className="flex gap-5 justify-end">
              <Button
                onClick={
                  displayedProductsType === "promoted"
                    ? openProductsDeletionPopUp
                    : openDiscountUploadPopUp
                }
                className={cn(
                  "px-4 h-10 rounded-sm text-white bg-blue hover:bg-blue/90 hover:text-white text-nowrap",
                  {
                    "bg-red hover:bg-red/90":
                      displayedProductsType === "promoted",
                  }
                )}
                id="manage-promotion-button"
              >
                <Text textStyle="TS5">
                  {displayedProductsType === "unpromoted"
                    ? buttonsContent("forms.upload.update")
                    : buttonsContent("forms.upload.delete")}
                </Text>
              </Button>
            </div>
          </div>
          {displayedProductsType === "unpromoted" && (
            <div className="mt-5 flex flex-col space-y-4">
              <Label htmlFor="byProducts" className="font-medium">
                {t("productsManagement.promotionType.title")}
              </Label>
              <RadioGroup
                value={promotionApplicationType}
                onValueChange={(value) =>
                  setPromotionApplicationType(
                    value as "byProducts" | "byCategories"
                  )
                }
                className="mt-4 flex flex-col L:flex-row gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="byProducts" id="byProducts" />
                  <label htmlFor="byProducts" className="font-medium">
                    <Text textStyle="TS7">
                      {t("productsManagement.promotionType.byProducts")}
                    </Text>
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="byCategories" id="byCategories" />
                  <label htmlFor="byCategories" className="font-medium">
                    <Text textStyle="TS7">
                      {t("productsManagement.promotionType.byCategories")}
                    </Text>
                  </label>
                </div>
              </RadioGroup>
            </div>
          )}
          <CategoriesSelection setSelectedCategories={setSelectedCategories} />

          {promotionApplicationType === "byProducts" && products.length > 0 && (
            <label className="mt-8 w-fit flex space-x-2 items-center">
              <Checkbox
                checked={
                  displayedProductsType === "promoted"
                    ? products.flatMap((product) => product.items).length ===
                      productItemIdsToDelete.length
                    : products.flatMap((product) => product.items).length ===
                      promotedProductItems.length
                }
                className="text-blue text-opacity-50 data-[state=checked]:bg-white outline-none bg-white w-5 h-5"
                onCheckedChange={(checked) => selectAllProducts(checked)}
              />
              <Text textStyle="TS7" className="font-bold cursor-pointer">
                {buttonsContent("pickers.selectAll")}
              </Text>
            </label>
          )}
        </div>
      }
    >
      <div className="flex flex-col space-y-3">
        {promotionApplicationType === "byProducts" &&
        products &&
        products.length > 0 ? (
          products.map((product) =>
            product.items.map((item, idx) => {
              return (
                <PromotionProductContainer
                  key={item.id}
                  productItem={item}
                  choosedFilter={displayedProductsType}
                  onEdit={() => {
                    setUploadModalIsOpen(true);
                    setPromotedProductItems([item]);
                  }}
                  onDelete={() => requestPromotionProductDeletion(item.id)}
                  checked={
                    displayedProductsType === "promoted"
                      ? productItemIdsToDelete.includes(item.id)
                      : promotedProductItems.some(
                          (productItem) => productItem.id === item.id
                        )
                  }
                  onCheck={handleProductCheck}
                  onDeleteCheck={handleProductDeletionCheck}
                  availablePromotion={displayedProductsType === "promoted"}
                />
              );
            })
          )
        ) : (
          <NoDataFound className="py-5 flex justify-center items-center" />
        )}
        {promotionApplicationType === "byProducts" && totalPages > 1 && (
          <div className="pt-3 px-3">
            <PaginationMangement
              records={records}
              pagesNumber={totalPages}
              currentPage={currentPage}
              changePage={changePage}
            />
          </div>
        )}
        {products && (
          <DiscountUpload
            isOpen={uploadModalIsOpen}
            setIsOpen={setUploadModalIsOpen}
            close={() => {
              setUploadModalIsOpen(false);
            }}
            onCurrencyChange={(currency) => setCurrency(currency)}
            isPending={isPending}
            onUpload={() =>
              updatePromotion(selectedCategories.map((cat) => cat.id))
            }
            warning={warning}
            currency={currency}
            discountType={discountType}
            setDiscountType={setDiscountType}
          />
        )}
        <AlertDialog open={alertModalIsOpen}>
          <ModalDialog
            title={t(
              "productsManagement.promotionProductsDeletionDialog.title"
            )}
            details={t(
              "productsManagement.promotionProductsDeletionDialog.details"
            )}
            cancel={t(
              "productsManagement.promotionProductsDeletionDialog.cancel"
            )}
            confirm={t(
              "productsManagement.promotionProductsDeletionDialog.confirm"
            )}
            theme="red"
            isPending={deletionIsPending}
            onCancel={cancelDeletion}
            onConfirm={deletePromotionProducts}
            warning={deletionWarning}
          />
        </AlertDialog>
      </div>
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton
      headerElement={<Skeleton className="h-7 L:w-40 w-32" />}
    >
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
