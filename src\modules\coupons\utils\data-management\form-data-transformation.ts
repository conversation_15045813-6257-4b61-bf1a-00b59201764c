import { UploadedCouponType } from "../../types/coupons";

export function transformFormDataToCoupon(
  formData: FormData
): UploadedCouponType {
  const toBoolean = (value: FormDataEntryValue | null): boolean =>
    value === "true";

  const couponData: UploadedCouponType = {
    code: formData.get("code") as string,
    description: formData.get("description") as string,
    maxRedemptions: formData.get("maxRedemptions")
      ? Number(formData.get("maxRedemptions"))
      : undefined,
    maxUsesPerUser: formData.get("maxUsesPerUser")
      ? Number(formData.get("maxUsesPerUser"))
      : 1,
    productItemIds: [],
    allowOnPromotions: formData.get("allowOnPromotions") === "on",
    unlimitedUsage: toBoolean(formData.get("unlimitedUsage")),
    freeShipping: toBoolean(formData.get("freeShipping")),
    period: {
      from: formData.get("period.from")
        ? new Date(formData.get("period.from") as string).toISOString()
        : undefined,
      to: formData.get("period.to")
        ? new Date(formData.get("period.to") as string).toISOString()
        : undefined,
      forever: toBoolean(formData.get("forever")),
    },
    totalVoucherRedemptionsByUsers: 0,
  };

  //applies to all product is applied when the coupon code is a real discount
  if (!couponData.freeShipping)
    couponData.appliesToAllProducts = toBoolean(
      formData.get("appliesToAllProducts")
    );

  if (!couponData.freeShipping) {
    couponData.discount = {
      type: formData.get("discountType") as "percentage" | "amount",
      value: Number(formData.get("discount.value")),
    };
  }

  return couponData;
}
