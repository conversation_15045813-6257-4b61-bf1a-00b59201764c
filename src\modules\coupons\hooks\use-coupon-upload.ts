import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { validateCouponData } from "../validation/validate-coupon-data";
import uploadCouponToServerSide from "../services/coupon-upload";
import { transformFormDataToCoupon } from "../utils/data-management/form-data-transformation";

type DiscountType = "amount" | "percentage" | "free";

export default function useCouponUpload(elementId?: string) {
  const queryClient = useQueryClient();
  const [isPending, setIsPending] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);
  const t = useTranslations("warnings");
  const [warning, setWarning] = useState("");

  const [selectedDiscountType, setSelectedDiscountType] =
    useState<DiscountType>("amount");
  const [isFreeShipping, setIsFreeShipping] = useState(false);
  const [isForever, setIsForever] = useState(false);
  const [isUnlimitedUsage, setIsUnlimitedUsage] = useState(false);

  const handleDiscountTypeChange = (type: DiscountType) => {
    setSelectedDiscountType(type);
    setIsFreeShipping(type === "free");

    if (formRef.current) {
      const form = formRef.current;

      // update hidden inputs
      const discountTypeInput = form.discountType as HTMLInputElement;
      const freeShippingInput = form.freeShipping as HTMLInputElement;

      if (discountTypeInput) {
        discountTypeInput.value = type === "free" ? "" : type;
      }

      if (freeShippingInput) {
        freeShippingInput.value = type === "free" ? "true" : "false";
      }

      // correct discount value input
      const discountValueInput = form["discount.value"] as HTMLInputElement;
      if (discountValueInput) {
        if (type === "free") {
          discountValueInput.value = "";
          discountValueInput.disabled = true;
        } else {
          discountValueInput.disabled = false;
        }
      }
    }
  };

  async function submitCoupon(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();

    try {
      const formData = new FormData(formRef.current);
      const couponData = transformFormDataToCoupon(formData);

      validateCouponData(couponData);
      await uploadCouponToServerSide(couponData, elementId);
      queryClient.invalidateQueries({ queryKey: ["coupon"], exact: false });
      router.push("/coupons");
    } catch (error) {
      const customError = error as CustomError;
      handleError(customError);
    } finally {
      setIsPending(false);
    }
  }

  const handleForeverChange = (checked: boolean) => {
    setIsForever(checked);

    if (formRef.current) {
      const form = formRef.current;

      const fromInput = form["period.from"] as HTMLInputElement;
      const toInput = form["period.to"] as HTMLInputElement;

      if (checked) {
        fromInput.value = "";
        toInput.value = "";
      }

      fromInput.disabled = checked;
      toInput.disabled = checked;
    }
  };

  const handleUnlimitedUsageChange = (checked: boolean) => {
    setIsUnlimitedUsage(checked);

    if (formRef.current) {
      const form = formRef.current;

      const maxRedemptionsInput = form.maxRedemptions as HTMLInputElement;

      if (checked) {
        maxRedemptionsInput.value = "";
      }

      maxRedemptionsInput.disabled = checked;
    }
  };

  function handleError(error: CustomError) {
    if (error.status === 500) {
      setWarning(t("serverError"));
      toast({ title: t("warning"), description: t("serverError") });
    } else if (error.status === 400) {
      const errorMap: Record<string, string> = {
        "Missed Data!": "upload.missedData",
        "The provided data is invalid!": "upload.invalidFormat",
        "A voucher code already exists with the same code!":
          "upload.coupons.couponCodeTaken",
        invalidPercentageValue: "upload.coupons.invalidPercentageValue",
        maxRedemptionsLessThanMaxUsesPerUser:
          "upload.coupons.maxRedemptionsLessThanMaxUsesPerUser",
        maxRedemptionsRequired: "upload.coupons.maxRedemptionsRequired",
        missingPeriodDates: "upload.coupons.missingPeriodDates",
        invalidDateRange: "upload.coupons.invalidDateRange",
      };

      const errorMessage = errorMap[error.message] || "upload.invalidFormat";
      setWarning(t(errorMessage));
      toast({ title: "Warning", description: t(errorMessage) });
    }
  }

  return {
    formRef,
    submitCoupon,
    warning,
    isPending,
    selectedDiscountType,
    isFreeShipping,
    isForever,
    isUnlimitedUsage,
    handleDiscountTypeChange,
    handleForeverChange,
    handleUnlimitedUsageChange,
  };
}
