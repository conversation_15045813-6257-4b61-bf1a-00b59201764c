import {
  PromotionItemInResponseType,
  PromotionItemType,
  PromotionProductInResponseType,
  PromotionProductType,
} from "../../types/products";

export function castToPromotionProductType(
  productInResponse: PromotionProductInResponseType
): PromotionProductType {
  return {
    brand: productInResponse.brand,
    categoryIds: productInResponse.categoryIds,
    name: productInResponse.name as string,
    displayOrder: productInResponse.displayOrder,
    description: productInResponse.description as string,
    id: productInResponse.id,
    items: productInResponse.items.map((item) =>
      castToPromotionItemType(item, productInResponse)
    ),
  };
}

function castToPromotionItemType(
  item: PromotionItemInResponseType,
  product: PromotionProductInResponseType
): PromotionItemType {
  return {
    name: product.name,
    id: item.id,
    reference: item.reference,
    barcode: item.barcode,
    quantity: item.quantity,
    image: `${process.env.BACKEND_ADDRESS}${
      item.image ? item.image : `/${item.image}`
    }`,
    images: item.images.map((image) => (image ? image : `/${image}`)),
    variations: item.variation || [],
    prices: item.prices.map((price) => {
      return {
        promotionalPrice: Number(price.promotionalPrice),
        realPrice: Number(price.regularPrice),
        currency: price.currency,
      };
    }),
    inStock: item.inStock,
  };
}
