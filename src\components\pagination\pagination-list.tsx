import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { PaginationItemType } from "@/types/pagination";

export function PaginationItem({ isActive, ...props }: PaginationItemType) {
  return (
    <div
      className={cn(
        props.className,
        `L:h-9 L:w-9 M:h-7 M:w-7 h-[26px] w-[26px] rounded-sm flex items-center justify-center text-gray  ${
          isActive ? " text-blue  bg-light-blue" : ""
        } cursor-pointer`
      )}
      {...props}
    >
      <Text textStyle="TS5">{props.children}</Text>
    </div>
  );
}

export function PaginationEllipsis() {
  return (
    <div
      className={`L:h-9 L:w-9 M:h-7 M:w-7 h-[26px] w-[26px] pb-2 rounded-sm flex items-end justify-center text-gray`}
    >
      <Text textStyle="TS4">...</Text>
    </div>
  );
}

export function PaginationNext({
  className,
  ...props
}: React.ComponentProps<"button">) {
  return (
    <button
      className={cn(
        "text-gray px-3 py-1 sm:px-2 sm:py-1 text-sm sm:text-xs",
        className
      )}
      id="pagination-next-button"
      {...props}
    >
      <Text textStyle="TS5">{props.children}</Text>
    </button>
  );
}

export function PaginationPrev({
  className,
  ...props
}: React.ComponentProps<"button">) {
  return (
    <button
      className={cn(
        "text-gray px-3 py-1 sm:px-2 sm:py-1 text-sm sm:text-xs",
        className
      )}
      id="pagination-prev-button"
      {...props}
    >
      <Text textStyle="TS5">{props.children}</Text>
    </button>
  );
}

export function PaginationList({ children }: { children: React.ReactNode[] }) {
  return <div className={"flex items-center space-x-3"}>{children}</div>;
}
