import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import React, { HTMLAttributes } from "react";
import { Skeleton } from "./ui/skeleton";
import { Button } from "./ui/button";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { PlusIcon } from "lucide-react";

interface Props extends HTMLAttributes<"div"> {
  title?: string;
  titleWrapper?: React.ReactNode;
  headerElement?: React.ReactNode;
  children: React.ReactNode | React.ReactNode[];
  parentClassName?: string;
  includesSearchbar?: boolean;
}

export default function DashboardCouponsListsContainer(props: Props) {
  const t = useTranslations("CouponsManagement");
  const router = useRouter();
  return (
    <>
      {props.titleWrapper ? (
        props.titleWrapper
      ) : (
        <div className="flex justify-between mb-9">
          <Text textStyle="TS5" className="font-bold">
            {props.title}
          </Text>
          <Button
            onClick={() => router.push("/coupons/addition")}
            className="bg-blue text-white self-end regularL:order-1 order-2 w-fit rounded-sm px-4 p-2 flex items-center  hover:bg-blue"
            id="delete-categories-button"
          >
            <PlusIcon />
            <Text textStyle="TS7">{t("add")}</Text>
          </Button>
        </div>
      )}
      <div
        className={cn(
          "shadow-md L:px-7 L:py-6 px-4 py-3 L:rounded-[30px] rounded-[10px] h-full bg-white border flex flex-col",
          props.parentClassName
        )}
      >
        <div
          className={cn(
            "border-b pb-7 w-full flex items-center justify-between gap-2",
            {
              "L:flex-row flex-col": props.includesSearchbar,
            }
          )}
        >
          {props.headerElement ? props.headerElement : null}
        </div>
        <div className={cn("flex-1", props.className)}>{props.children}</div>
      </div>
    </>
  );
}

export function DashboardCouponsListsContainerSkeleton({
  children,
  headerElement = null,
  ...props
}: {
  children: React.ReactNode | React.ReactNode[];
  headerElement?: React.ReactNode;
} & HTMLAttributes<"div">) {
  return (
    <div
      className={cn(
        "bg-white border L:px-7 L:py-6 px-4 py-3 L:rounded-[30px] rounded-[10px] h-full flex flex-col space-y-5",
        props.className
      )}
    >
      <div className="flex space-x-3 justify-between">
        <Skeleton className="h-7 L:w-48 w-32" />
        {headerElement}
      </div>
      {children}
    </div>
  );
}
