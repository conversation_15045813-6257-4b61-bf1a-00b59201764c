import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { castToBlogType } from "../utils/data-management/types-casting/blogs";
import { BlogInResponseType } from "../types/blogs";

export async function retrieveEditableBlogFromServerSide(blogSlug: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`/blogs/${blogSlug}/dashboard`, headers);
    return castToBlogType(res.data as BlogInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveEditableBlogFromServerSide(blogSlug)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
