import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { castToCategoryType } from "../../utils/data-management/types-casting/categories";
import { CategoryInResponseType } from "../../types/categories";

export async function retrieveEditableCategory(
  categorySlug: string,
  language?: string
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = language
      ? `/categories/${categorySlug}/dashboard?language=${language}`
      : `/categories/${categorySlug}/dashboard`;

    const res = await GET(endpoint, headers);

    return castToCategoryType(res.data as CategoryInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveEditableCategory(categorySlug, language)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
