import { BlogInResponseType, BlogType } from "@/modules/blogs/types/blogs";
import { castToMetaContentType } from "@/modules/seo/utils/types-casting/meta-content";

export function castToBlogType(blogInResponse: BlogInResponseType): BlogType {
  return {
    slug: blogInResponse.slug,
    id: blogInResponse.id,
    metaContent: blogInResponse.metaContent
      ? castToMetaContentType(blogInResponse.metaContent)
      : null,

    image: blogInResponse.image
      ? `${process.env.BACKEND_ADDRESS}${blogInResponse.image}`
      : null,
    title: blogInResponse.title,
    description: blogInResponse.description,
    details: blogInResponse.details,

    displayOrder: blogInResponse.displayOrder,
  };
}
