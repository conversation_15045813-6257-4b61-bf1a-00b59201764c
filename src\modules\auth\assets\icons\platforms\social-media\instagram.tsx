interface Props {
  color?: string;
}

export default function Instagram({ color = "white" }: Props) {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.666504 4.00002C0.666504 2.15907 2.15889 0.666687 3.99984 0.666687H13.9998C15.8408 0.666687 17.3332 2.15907 17.3332 4.00002V14C17.3332 15.8409 15.8408 17.3334 13.9998 17.3334H3.99984C2.15889 17.3334 0.666504 15.8409 0.666504 14V4.00002ZM3.99984 2.33335C3.07936 2.33335 2.33317 3.07955 2.33317 4.00002V14C2.33317 14.9205 3.07936 15.6667 3.99984 15.6667H13.9998C14.9203 15.6667 15.6665 14.9205 15.6665 14V4.00002C15.6665 3.07955 14.9203 2.33335 13.9998 2.33335H3.99984ZM8.99984 6.50002C7.61909 6.50002 6.49984 7.61927 6.49984 9.00002C6.49984 10.3808 7.61909 11.5 8.99984 11.5C10.3806 11.5 11.4998 10.3808 11.4998 9.00002C11.4998 7.61927 10.3806 6.50002 8.99984 6.50002ZM4.83317 9.00002C4.83317 6.69884 6.69865 4.83335 8.99984 4.83335C11.301 4.83335 13.1665 6.69884 13.1665 9.00002C13.1665 11.3012 11.301 13.1667 8.99984 13.1667C6.69865 13.1667 4.83317 11.3012 4.83317 9.00002ZM13.5832 5.66669C14.2735 5.66669 14.8332 5.10704 14.8332 4.41669C14.8332 3.72633 14.2735 3.16669 13.5832 3.16669C12.8928 3.16669 12.3332 3.72633 12.3332 4.41669C12.3332 5.10704 12.8928 5.66669 13.5832 5.66669Z"
        fill={color}
      />
    </svg>
  );
}
