import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { ProductInResponseType } from "../../catalog/types/products";
import { castToProductType } from "../../catalog/utils/data-management/types-casting/products";
import { PaginationType } from "@/types/pagination";

export default async function retrieveTopSellingProducts(
  page: number,
  limit: number
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/products";

    const res = await GET(`${endpoint}?page=${page}&limit=${limit}`, headers);

    return {
      data: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse)
      ),
      pagination: res.data.pagination as PaginationType,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveTopSellingProducts(page, limit)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
