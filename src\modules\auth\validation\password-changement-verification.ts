import { getPasswordChangementWarnings } from "@auth/utils/warnings/password-changement/input-warning";
import { getPasswordChangementSchema } from "./schemas/auth/password-changement";

type ResponseType = {
  ok: boolean;
  warning: {
    currentPassword: string;
    newPassword: string;
    confirmationPassword: string;
  };
};

type Passwords = {
  currentPassword: string;
  newPassword: string;
  confirmationPassword: string;
};

export function verifyPasswordChangementData(
  passwords: Passwords
): ResponseType {
  const passwordConfirmationSchema = getPasswordChangementSchema();
  const verificationResult = passwordConfirmationSchema.safeParse(passwords);

  if (!verificationResult.success) {
    return {
      ok: false,
      warning: getPasswordChangementWarnings(verificationResult),
    };
  } else
    return {
      ok: true,
      warning: {
        currentPassword: "",
        newPassword: "",
        confirmationPassword: "",
      },
    };
}
