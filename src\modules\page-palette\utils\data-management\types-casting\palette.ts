import {
  PaletteInResponseType,
  PaletteType,
} from "@/modules/page-palette/types/palette";

export function castToPaletteType(
  paletteInResponse: PaletteInResponseType
): PaletteType {
  return {
    id: paletteInResponse.id,
    name: paletteInResponse.name,
    images: paletteInResponse.images.map((image) => ({
      computerImage: `${process.env.BACKEND_ADDRESS}${
        image.computerImage || ""
      }`,
      mobileImage: `${process.env.BACKEND_ADDRESS}${image.mobileImage || ""}`,
      link: image.redirectUrl,
    })),
    sections: paletteInResponse.sections
      ? paletteInResponse.sections.map(({ id, title, description }) => ({
          id,
          description,
          title,
        }))
      : [],
    metaContent: paletteInResponse.metaContent
      ? {
          title: paletteInResponse.metaContent.title,
          description: paletteInResponse.metaContent.description,
          keywords: paletteInResponse.metaContent.tags,
        }
      : null,
    displayOrder: paletteInResponse.displayOrder,
  };
}
