export default function cleanProductFormData(
  formData: FormData,
  elementId?: string
): FormData {
  const filteredFormData = new FormData();
  const categoryIds: string[] = [];

  formData.forEach((value, key) => {
    if (key === "displayOrder") {
      //posting display order if user change it
      if ((value as string).trim() !== "") filteredFormData.append(key, value);
    } else {
      // Remove subTypeId and typeId
      if (key === "categoryIds") {
        categoryIds.push(value as string);
      } else if (
        //we seperate creation from edition bc in edition we can put empty fields to remove categories and brands...
        !elementId && //product creation
        (value as string).trim() !== ""
      ) {
        filteredFormData.append(key, value);
      } else if (elementId) {
        //product edition
        if (key === "brandId" && value === "") {
          filteredFormData.append(key, "null");
        } else filteredFormData.append(key, value);
      }
    }
  });

  categoryIds.forEach((id) => filteredFormData.append("categoryIds", id));

  return filteredFormData;
}
