import Text from "@/styles/text-styles";
import TrashBin from "@assets/icons/trash-bin";
import { useTranslations } from "next-intl";
import Image from "next/image";
import React, { Dispatch, SetStateAction, useRef, useState } from "react";

interface Props {
  name: string;
  defaultPreviews?: string[];
  setDefaultPreviews?: Dispatch<SetStateAction<string[]>>;
  setNewImages?: Dispatch<SetStateAction<File[]>>;
  setDeletedImages?: Dispatch<SetStateAction<string[]>>;
  mode: "creation" | "edition";
}

export default function ImagesUpload({
  name,
  defaultPreviews,
  setDefaultPreviews,
  setNewImages,
  setDeletedImages,
  mode,
}: Props) {
  const imagesInputRef = useRef<HTMLInputElement>(null);
  const [previews, setPreviews] = useState<string[]>([]);
  const t = useTranslations("shared.forms.upload.file");

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (files && imagesInputRef.current) {
      const fileBuffer = new DataTransfer();
      const newFiles: File[] = [];
      const newPreviews: string[] = [];

      Array.from(files).forEach((file) => {
        if (file.type.startsWith("image")) {
          fileBuffer.items.add(file);
          newFiles.push(file);
          newPreviews.push(URL.createObjectURL(file));
        }
      });

      imagesInputRef.current.files = fileBuffer.files;
      setPreviews((prev) => [...prev, ...newPreviews]);

      if (mode === "edition" && setNewImages) {
        setNewImages((prev) => [...(prev || []), ...newFiles]);
      }
    }
  };

  const removeImage = (index: number, isDefault: boolean) => {
    if (
      isDefault &&
      mode === "edition" &&
      setDefaultPreviews &&
      setDeletedImages
    ) {
      setDefaultPreviews((prev) => {
        const updatedPreviews = prev.filter((_, i) => i !== index);
        setDeletedImages((deleted) => [...deleted, prev[index]]);
        return updatedPreviews;
      });
    } else {
      setPreviews((prev) => prev.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="w-full flex flex-col space-y-4">
      <div className="flex-grow flex flex-wrap gap-4 ">
        <label className="w-full 2L:w-1/4 rounded-md border-dashed border-2 min-h-40 flex flex-col items-center justify-center cursor-pointer p-4 text-center">
          <Text
            textStyle="TS7"
            className="py-2 px-3 border rounded-lg text-blue"
          >
            {t("button")}
          </Text>
          <Text textStyle="TS7" className="text-gray mt-2">
            {t("description")}
          </Text>
          <input
            ref={imagesInputRef}
            type="file"
            accept="image/*"
            name={name}
            multiple
            onChange={handleImageChange}
            className="hidden"
          />
        </label>
        {defaultPreviews?.map((preview, index) => (
          <div
            key={`default-preview${index}`}
            className="relative group w-[200px] h-[200px]"
          >
            <Image
              src={preview}
              unoptimized
              alt="default Preview"
              layout="fill"
              objectFit="cover"
              className="rounded-lg"
            />
            <div
              onClick={() => removeImage(index, true)}
              className="absolute top-[-10px] right-[-15px] bg-red text-white border-[2px] border-white rounded-full w-14 h-10 flex items-center justify-center group-hover:opacity-100 transition-opacity cursor-pointer"
            >
              <TrashBin />
            </div>
          </div>
        ))}
        {previews.map((preview, index) => (
          <div
            key={`preview${index}`}
            className="relative group w-[200px] h-[200px]"
          >
            <Image
              src={preview}
              unoptimized
              alt="Preview"
              layout="fill"
              objectFit="cover"
              className="rounded-lg"
            />
            <div
              onClick={() => removeImage(index, false)}
              className="absolute top-[-10px] right-[-15px] bg-red text-white border-[2px] border-white rounded-full w-14 h-10 flex items-center justify-center group-hover:opacity-100 transition-opacity cursor-pointer"
            >
              <TrashBin />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
