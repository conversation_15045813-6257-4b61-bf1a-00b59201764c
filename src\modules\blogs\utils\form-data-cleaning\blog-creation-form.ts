export default function cleanBlogCreationFormData(
  formData: FormData
): FormData {
  const filteredFormData = new FormData();
  const content: {
    name: string;
    description: string;
    details: string;
    language: string;
  }[] = [];
  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  // Get all languages data
  ["arabic", "french", "english"].forEach((lang) => {
    const nameField = formData.get(`name_${lang}`);
    const descField = formData.get(`description_${lang}`);
    const detailsField = formData.get(`details_${lang}`);

    const nameValue = nameField?.toString() || "";
    const descValue = descField?.toString() || "";
    const detailsValue = detailsField?.toString() || "";

    if (nameValue || descValue || detailsValue) {
      content.push({
        name: nameValue,
        description: descValue,
        details: detailsValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  // Append non-language specific fields
  for (const [key, value] of formData.entries()) {
    if (excludedFields.includes(key)) {
      continue;
    }

    if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      filteredFormData.append(key, value);
    }
  }

  if (content.length > 0) {
    filteredFormData.append("content", JSON.stringify(content));
  }

  return filteredFormData;
}
