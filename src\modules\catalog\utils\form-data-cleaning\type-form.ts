export default function cleanTypeFormData(
  formData: FormData,
  elementId?: string
): FormData {
  const filteredFormData = new FormData();
  let imageDeleted = false;

  formData.forEach((value, key) => {
    if (key === "deleted-image" && value === "null") {
      imageDeleted = true;
      return;
    }

    //image addition
    if (key === "image" && typeof value === "object") {
      if (value.size !== 0) filteredFormData.append(key, value);
    } else if (!elementId && typeof value === "string" && value.trim() !== "") {
      // type creation
      filteredFormData.append(key, value);
    } else if (elementId) {
      // type edition
      filteredFormData.append(key, value);
    }
  });

  // If the image was deleted, add an empty image field to filteredFormData
  if (imageDeleted && !filteredFormData.get("image")) {
    filteredFormData.append("image", "null");
  }

  if (formData.has("keywords")) {
    filteredFormData.delete("keywords");
  }
  return filteredFormData;
}
