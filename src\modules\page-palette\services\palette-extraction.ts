import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types/pagination";
import { castToPaletteType } from "../utils/data-management/types-casting/palette";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { AxiosError } from "axios";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";

export async function retrievePagePaletteFromServerSide() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/page-palette/landing-page`;

    const res = await GET(endpoint, headers);

    return {
      pagination: res.data.pagination as PaginationType,
      palettes: castToPaletteType(res.data),
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrievePagePaletteFromServerSide());

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
