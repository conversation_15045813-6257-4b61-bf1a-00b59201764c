import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import React, { HTMLAttributes } from "react";

interface Props extends HTMLAttributes<"div"> {
  icon: React.ReactNode;
  title: string;
  description: string;
  value: string;
  moreContent?: React.ReactNode;
  isLoading?: boolean;
  skeletonsClassname?: string;
}

export default function MetricCard({
  moreContent = null,
  isLoading = false,
  skeletonsClassname = "",
  ...props
}: Props) {
  return !isLoading ? (
    <div
      className={cn(
        "shadow-md group hover:bg-purple hover:text-white bg-white text-black border L:p-5 p-3 rounded-[10px] flex flex-col space-y-6",
        props.className
      )}
    >
      <div className="flex flex-col">
        <div className="w-full flex items-center justify-between">
          <Text textStyle="TS5" className="">
            {props.title}
          </Text>
          <div
            className={cn(
              "w-[38px] h-[38px] rounded-[5px] flex justify-center items-center bg-white",
              {
                "bg-[#F7F7F7]": props.className?.includes("bg-white"),
              }
            )}
          >
            {props.icon}
          </div>
        </div>
        <Text textStyle="TS7" className="text-gray group-hover:text-white">
          {props.description}
        </Text>
      </div>
      <div className="flex flex-col space-y-1">
        <Text textStyle="TS4" className="font-bold">
          {props.value}
        </Text>
        {moreContent}
      </div>
    </div>
  ) : (
    <MetricCardSkeleton
      className={cn("bg-white", props.className, skeletonsClassname)}
    />
  );
}

export function MetricCardSkeleton(props: HTMLAttributes<"div">) {
  return (
    <div
      className={cn(
        "L:p-5 p-3 rounded-[10px] flex flex-col space-y-6",
        props.className
      )}
    >
      <div className="w-full flex items-center justify-between space-x-1">
        <Skeleton className="w-full max-w-[150px] h-7" />
        <Skeleton className="w-full max-w-[38px] h-[38px]" />
      </div>
      <div className="flex flex-col space-y-1">
        <Skeleton className="w-full max-w-[150px] h-7" />
        <Skeleton className="w-full max-w-[200px] h-7" />
      </div>
    </div>
  );
}
