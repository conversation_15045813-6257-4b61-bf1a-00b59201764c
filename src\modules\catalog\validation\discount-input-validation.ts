import { DiscountType } from "@/modules/promotions/types";
import { CustomError } from "@/utils/custom-error";

export default function validateDiscountInput(
  id: string,
  discountType: DiscountType
) {
  const discount = document.getElementById(id) as HTMLInputElement;

  if (discountType === "amount") return Number(discount.value);

  if (discount) {
    const discountValue = Number(discount.value);

    if (discountValue <= 100 && discountValue > 0) return discountValue;

    throw new CustomError("Invalid Data!", 400);
  }

  return new CustomError("Missed Data!", 400);
}
