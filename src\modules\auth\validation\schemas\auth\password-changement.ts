import { z } from "zod";

const EnglishPasswordChangementSchema = z
  .object({
    currentPassword: z.string().min(8, {
      message: "Le mot de passe doit contenir au moins 8 caractères",
    }),
    newPassword: z.string().min(8, {
      message: "Le mot de passe doit contenir au moins 8 caractères",
    }),
    confirmationPassword: z.string().min(8, {
      message: "Le mot de passe doit contenir au moins 8 caractères",
    }),
  })
  .refine((data) => data.newPassword === data.confirmationPassword, {
    message: "Les mots de passe ne correspondent pas",
    path: ["confirmationPassword", "generalWarning"],
  });

export function getPasswordChangementSchema() {
  return EnglishPasswordChangementSchema;
}
