import BagIcon from "@assets/icons/bag-icon";
import { Order } from "../../types/orders";
import Text from "@/styles/text-styles";
import { cn } from "@/lib/utils";
import useOrderStatusEdition from "../../hooks/use-order-status-edition";
import OrderStatusDropDown from "../status-drop-down";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";

interface Props {
  order: Order;
  onClick?: () => void;
}

export default function OrderManagementContainer(props: Props) {
  const { isPending, updateOrderStatus, orderStatusList, warning } =
    useOrderStatusEdition(() => {});
  const t = useTranslations("ordersManagement");
  const orderDate = new Date(props.order.createdAt)
    .toLocaleString("en-CA", {
      month: "short",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    })
    .replace(",", "")
    .replace(" ", "-");

  return (
    <div className="border-t pt-4 flex justify-end">
      <div className="flex flex-col space-y-2">
        <div
          className={cn(
            "font-medium basis-1/2 flex extraXL:flex-row flex-col extraXL:space-x-5"
          )}
        >
          <div className="flex space-x-3">
            <BagIcon />
            <div className="flex extraXL:flex-row flex-col extraXL:space-x-2">
              <div className="flex flex-col M:w-[200px] w-[100px] ">
                <Text
                  textStyle="TS6"
                  className="font-bold text-dark-blue overflow-hidden text-ellipsis"
                >
                  {t("order")} {props.order.code}
                </Text>
                <Text textStyle="TS6" className="text-gray">
                  {`${(
                    props.order.total - props.order.couponCodeDiscount
                  ).toFixed(3)} ${props.order.currency}`}
                </Text>
              </div>
              <Text
                textStyle="TS6"
                className="text-dark-blue font-bold"
              >{`${props.order.address.firstName} ${props.order.address.lastName}`}</Text>
            </div>
          </div>
        </div>
        <Text
          textStyle="TS6"
          className="3L:hidden px-3 py-2 w-fit h-fit bg-[#F7F9FC] text-gray rounded-full"
        >
          {orderDate}
        </Text>
      </div>
      <div className="flex-1 flex 3L:flex-row flex-col 3L:justify-end justify-start 3L:items-center items-end 3L:space-y-0 space-y-3">
        <div className="3L:flex-1 flex items-center 3L:justify-center space-x-1">
          <Text
            textStyle="TS6"
            className="3L:flex hidden px-3 py-2 w-fit h-fit bg-[#F7F9FC] text-gray rounded-full"
          >
            {orderDate}
          </Text>
          <OrderStatusDropDown
            orderId={props.order.id}
            status={props.order.status}
            orderStatusList={orderStatusList}
            onClick={updateOrderStatus}
          />
        </div>
        <div className="flex items-center">
          <Button
            onClick={props.onClick ? props.onClick : () => {}}
            className="w-fit bg-transparent shadow-none hover:bg-transparent"
            id="learn-more-button"
          >
            <Text
              textStyle="TS6"
              className="text-blue relative before:content-[''] before:absolute before:right-0 before:left-0 before:h-[1px] before:-bottom-1 before:bg-blue"
            >
              {t("learnMore")}
            </Text>
          </Button>
        </div>
      </div>
    </div>
  );
}
