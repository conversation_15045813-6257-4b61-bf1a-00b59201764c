import { BrandInResponseType, BrandType } from "@/modules/catalog/types/brands";
import {
  MultilanguageBrandInResponseType,
  MultilanguageBrandType,
} from "@/modules/catalog/types/multilanguage-brands";

export function castToBrandType(
  brandInResponse: BrandInResponseType
): BrandType {
  return {
    metaContent: brandInResponse.metaContent
      ? {
          title: brandInResponse.metaContent.title,
          description: brandInResponse.metaContent.description,
          keywords: brandInResponse.metaContent.tags,
        }
      : null,
    id: brandInResponse.id,
    name: brandInResponse.name,
    image: `${process.env.BACKEND_ADDRESS}${
      brandInResponse.image ? brandInResponse.image : ""
    }`,
    description: brandInResponse.description,
    displayOrder: brandInResponse.displayOrder,
    slug: brandInResponse.slug,
  };
}

export function castToMultilanguageBrandType(
  brandInResponse: MultilanguageBrandInResponseType
): MultilanguageBrandType {
  return {
    metaContent: brandInResponse.metaContent
      ? {
          title: brandInResponse.metaContent.title,
          description: brandInResponse.metaContent.description,
          keywords: brandInResponse.metaContent.tags,
        }
      : null,
    id: brandInResponse.id,
    content: brandInResponse.content,
    image: `${process.env.BACKEND_ADDRESS}${
      brandInResponse.image ? brandInResponse.image : ""
    }`,
    displayOrder: brandInResponse.displayOrder,
    slug: brandInResponse.slug,
  };
}
