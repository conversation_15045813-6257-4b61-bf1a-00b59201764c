"use client";
import { HTMLAttributes } from "react";
import Text from "@/styles/text-styles";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useTranslations } from "next-intl";
import { CustomerType } from "../types/customers";

interface Props extends HTMLAttributes<"div"> {
  customers: CustomerType[];
}

export default function CustomersTable({ customers }: Props) {
  const t = useTranslations("CustomersManagement");

  return (
    <Table>
      <TableHeader>
        <TableRow className="w-full bg-[#F7F9FC] hover:bg-[#F7F9FC]">
          {t.raw("customerListColumns").map((label: string, idx: number) => (
            <TableCell key={idx} className="text-[#6F7182] py-5">
              <Text textStyle="TS7">{label}</Text>
            </TableCell>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className="min-w-full text-[#6F7182]">
        {customers.map((customer) => (
          <TableRow key={customer.id}>
            <TableCell className="font-medium flex items-center space-x-3 cursor-pointer">
              <div className="flex items-center space-x-4">
                <div className="rounded-full bg-gray flex items-center justify-center px-5 py-3">
                  <Text
                    textStyle="TS5"
                    className="font-bold text-white uppercase"
                  >
                    {customer.name[0]}
                  </Text>
                </div>
                <Text
                  textStyle="TS6"
                  className="text-sm font-semibold text-gray-800"
                >
                  {customer.name}
                </Text>
              </div>
            </TableCell>

            <TableCell>
              <Text textStyle="TS6" className="flex-1 flex text-black">
                {customer.email}
              </Text>
            </TableCell>
            <TableCell>
              <Text textStyle="TS6" className="flex-1 flex text-black">
                {customer.points}
              </Text>
            </TableCell>
            {/* <TableCell>
              <Text
                textStyle="TS6"
                className="flex-1 flex justify-center text-black"
              >
                {customer.points}
              </Text>
            </TableCell>
            <TableCell>
              <Text textStyle="TS7" className="flex-1 flex justify-center">
                {customer.ordersNumber}
              </Text>
            </TableCell>
            <TableCell>
              <Text
                textStyle="TS6"
                className="flex-1 flex justify-center text-black"
              >
                {`$ ${customer.totalRevenue.toFixed(2)}`}
              </Text>
            </TableCell> */}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
