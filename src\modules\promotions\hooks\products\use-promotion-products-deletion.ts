import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { deletePromotionProductOnServerSide } from "../../services/products/promotion-product-deletion";
import { CheckedState } from "@radix-ui/react-checkbox";

export default function usePromotionProductsDeletion(promotionId: string) {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);
  const [productItemIds, setProductItemIds] = useState<string[]>([]);

  const requestPromotionProductsDeletion = () => {
    setAlertModalIsOpen(true);
  };

  const requestPromotionProductDeletion = (productItemId: string) => {
    setProductItemIds([productItemId]);
    setAlertModalIsOpen(true);
  };

  const deletePromotionProducts = async () => {
    setIsPending(true);

    if (warning !== "") setWarning("");

    try {
      await Promise.all(
        productItemIds.map((productItemId) =>
          deletePromotionProductOnServerSide(promotionId, productItemId)
        )
      );

      if (warning !== "") setWarning("");

      setAlertModalIsOpen(false);

      queryClient.invalidateQueries({
        queryKey: ["promotion-products"],
        exact: false,
      });
    } catch (throwedError) {
      const error = throwedError as CustomError;
      if (error.status === 500) setWarning(t("serverError"));
    }

    setIsPending(false);
  };

  const handleProductDeletionCheck = (
    checked: CheckedState,
    productItemId: string
  ) => {
    if (checked) {
      setProductItemIds((productItemIds) => [...productItemIds, productItemId]);
    } else {
      const filteredProductItems = productItemIds.filter(
        (checkedProductItemId) => checkedProductItemId !== productItemId
      );

      setProductItemIds(filteredProductItems);
    }
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
    setProductItemIds([]);
  };

  return {
    isPending,
    alertModalIsOpen,
    cancelDeletion,
    warning,
    deletePromotionProducts,
    requestPromotionProductsDeletion,
    handleProductDeletionCheck,
    productItemIdsToDelete: productItemIds,
    requestPromotionProductDeletion,
  };
}
