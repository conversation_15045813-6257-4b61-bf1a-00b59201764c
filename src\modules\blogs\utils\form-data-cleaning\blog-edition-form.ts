export function cleanBlogEditionFormData(
  formData: FormData,
  currentLanguage: string
): FormData {
  const cleanedData = new FormData();

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  for (const [key, value] of formData.entries()) {
    if (excludedFields.includes(key)) {
      continue;
    }

    if (key.includes(`_${currentLanguage}`)) {
      const normalFieldName = key.replace(`_${currentLanguage}`, "");
      cleanedData.append(normalFieldName, value);
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      cleanedData.append(key, value);
    }
  }

  return cleanedData;
}
