"use client";

import { cn } from "@/lib/utils";
import { ProductSalesType } from "../../../types/catalog";
import Text from "@/styles/text-styles";
import Image from "next/image";
import { useEffect, useState } from "react";

interface Props {
  product: ProductSalesType;
  last?: boolean;
}

export default function TopSellingProductContainer({
  product,
  last = false,
}: Props) {
  const [productImage, setProductImage] = useState("/not-found/image.png");

  useEffect(() => {
    if (product.image) setProductImage(product.image);
  }, [product]);

  return (
    <div
      className={cn("h-[100px] flex items-center justify-between space-x-2", {
        "border-b": !last,
      })}
    >
      <div className="flex space-x-7 items-center">
        {product.image ? (
          <Image
            alt={product.name}
            src={productImage}
            width={30}
            height={30}
            unoptimized
            onError={() => setProductImage("/not-found/image.png")}
          />
        ) : null}
        <Text textStyle="TS7" className="font-bold">
          {product.name}
        </Text>
      </div>
      <Text textStyle="TS6" className="font-bold text-green">
        {product.quantitySold}
      </Text>
    </div>
  );
}
