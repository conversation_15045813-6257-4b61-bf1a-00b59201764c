export interface PromotionInResponseType {
  id: string;
  slug: string;
  name: string;
  description: string;
  from: Date;
  to: Date;
  forever: boolean;
}

export interface PromotionType {
  slug: string;
  id: string;
  name: string;
  description: string;
  startTime: Date;
  endTime: Date;
  forever: boolean;
}

export interface UploadedPromotionType {
  name: string;
  description: string;
  from?: Date | string;
  to?: Date | string;
  forever: boolean;
}
