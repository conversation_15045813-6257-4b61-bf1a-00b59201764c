"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Image from "next/image";

export default function TawerBar() {
  const t = useTranslations("shared.navbar");

  return (
    <div className="relative w-full min-h-[171px]">
      <Image
        alt="gradient-image"
        src="/bg-effects/tawer-board-gradient-effect.png"
        width={1127}
        height={171}
        className="absolute left-0 right-0"
      />
      <div className="relative z-20 pb-7 min-h-[171px] h-full flex flex-col items-center space-y-2 justify-end">
        <Image
          alt="tawer-logo"
          src="/logos/tawer-logo.svg"
          width={242}
          height={55}
          className=""
        />
        <Text textStyle="TS5" className="text-purple text-center px-2">
          {t.rich("description", {
            b: (chunks) => <span className="font-bold">{chunks}</span>,
          })}
        </Text>
      </div>
    </div>
  );
}
