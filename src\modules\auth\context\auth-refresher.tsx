import { useState } from "react";

import { useContext, createContext } from "react";

interface AuthRefresherType {
  authRefresher: number;
  refreshUserAuthentication: () => void;
}

const AuthRefresher = createContext<AuthRefresherType>({
  authRefresher: 0,
  refreshUserAuthentication: () => {},
});

export default function useAuthRefresher() {
  const authRefresher = useContext(AuthRefresher);

  return authRefresher;
}

export function AuthRefresherProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [authRefresher, setAuthRefresher] = useState(0);

  function refreshUserAuthentication() {
    setAuthRefresher(authRefresher + 1);
  }

  return (
    <AuthRefresher.Provider
      value={{ authRefresher, refreshUserAuthentication }}
    >
      {children}
    </AuthRefresher.Provider>
  );
}
