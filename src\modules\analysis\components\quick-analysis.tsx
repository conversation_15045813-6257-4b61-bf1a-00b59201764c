"use client";
import Text from "@/styles/text-styles";
import MetricCard from "./metric-card";
import {
  CircleDashed,
  Clipboard,
  Database,
  LayoutGrid,
  UserCheck,
  Users,
} from "lucide-react";
import useUsersNumberOverview from "../hooks/use-users-number-overview";
import useProductsOverview from "../hooks/use-products-overview";
import useOrdersOverview from "../hooks/use-orders-overview";
import { useTranslations } from "next-intl";
import PercentageChange from "./percentage-change";
import useUser from "@/modules/auth/hooks/use-user";

export default function QuickAnalysis() {
  const { user, isLoading } = useUser();
  const t = useTranslations("dashboard.quickAnalysis");
  const { data: users, isLoading: usersAreLoading } = useUsersNumberOverview();
  const { data: products, isLoading: productsAreLoading } =
    useProductsOverview();
  const { data: orders, isLoading: ordersAreLoading } = useOrdersOverview();

  if (isLoading) {
    return null;
  }

  return (
    user &&
    user.role === "Admin" && (
      <div className="w-full flex flex-col space-y-5 pb-3">
        <div className="flex flex-col">
          <Text textStyle="TS4" className="font-bold">
            {t("title")}
          </Text>
        </div>
        <div className="flex flex-col space-y-4">
          <div className="flex L:flex-row flex-col gap-4">
            <MetricCard
              icon={<Users color="black" />}
              title={t("totalUsers.title")}
              description={t("totalUsers.description")}
              value={`${users ? users.usersNumberCurrentMonth : 0}`}
              isLoading={usersAreLoading || users === undefined}
              moreContent={
                users && (
                  <PercentageChange
                    x={users.usersNumberCurrentMonth}
                    y={users.usersNumberLastMonth}
                  />
                )
              }
              className="basis-1/3"
            />
            <MetricCard
              icon={<UserCheck color="black" />}
              title={t("addToCartEvent.title")}
              description={t("addToCartEvent.description")}
              value={`${
                users ? users.usersNumberWithFullCartsCurrentMonth : 0
              }`}
              isLoading={usersAreLoading || users === undefined}
              moreContent={
                users && (
                  <PercentageChange
                    x={users.usersNumberWithFullCartsCurrentMonth}
                    y={users.usersNumberWithFullCartsLastMonth}
                  />
                )
              }
              className="basis-1/3"
            />
            <MetricCard
              icon={<LayoutGrid color="black" />}
              title={t("totalProductsSold.title")}
              description={t("totalProductsSold.description")}
              value={`${products ? products.totalProductsSoldCurrentMonth : 0}`}
              isLoading={productsAreLoading || products === undefined}
              moreContent={
                products && (
                  <PercentageChange
                    x={products.totalProductsSoldCurrentMonth}
                    y={products.totalProductsSoldLastMonth}
                  />
                )
              }
              className="basis-1/3"
            />
          </div>
          <div className="flex L:flex-row flex-col gap-4">
            <MetricCard
              icon={<Database color="black" />}
              title={t("totalSales.title")}
              description={t("totalSales.description")}
              value={`${
                orders
                  ? `${orders.totalRevenueCurrentMonth} ${orders.currency}`
                  : 0
              }`}
              moreContent={
                orders && (
                  <PercentageChange
                    x={orders.totalRevenueCurrentMonth}
                    y={orders.totalRevenueLastMonth}
                  />
                )
              }
              isLoading={ordersAreLoading || orders === undefined}
              className="basis-1/3"
            />
            <MetricCard
              icon={<Clipboard color="black" />}
              title={t("averageOrders.title")}
              description={t("averageOrders.description")}
              value={`${
                orders
                  ? `${orders.averageRevenueCurrentMonth} ${orders.currency}`
                  : 0
              }`}
              moreContent={
                orders && (
                  <PercentageChange
                    x={orders.averageRevenueCurrentMonth}
                    y={orders.averageRevenueLastMonth}
                  />
                )
              }
              isLoading={ordersAreLoading || orders === undefined}
              className="basis-1/3"
            />
            <MetricCard
              icon={<CircleDashed color="black" />}
              title={t("quantitySold.title")}
              description={t("quantitySold.description")}
              value={`${orders ? orders.totalOrdersCurrentMonth : 0}`}
              moreContent={
                orders && (
                  <PercentageChange
                    x={orders.totalOrdersCurrentMonth}
                    y={orders.totalOrdersLastMonth}
                  />
                )
              }
              isLoading={ordersAreLoading || orders === undefined}
              className="basis-1/3"
            />
          </div>
        </div>
      </div>
    )
  );
}
