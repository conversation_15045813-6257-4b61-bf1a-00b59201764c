import { useState, useRef } from "react";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import { Button } from "@/components/ui/button";
import { Language } from "../../types/multilanguage-seo";

interface KeywordsSectionProps {
  keywords?: string[];
  addNewKeyword: (keyword: string) => void;
  removeKeyword: (keyword: string) => void;
  activeLanguage: Language;
  multilanguage: boolean;
}

export default function KeywordsSection({
  keywords = [],
  addNewKeyword,
  activeLanguage,
  multilanguage,
  removeKeyword,
}: KeywordsSectionProps) {
  const [keywordInput, setKeywordInput] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const uploadContent = useTranslations(
    "shared.forms.upload.seoLabels.keywords"
  );

  const getFieldName = (fieldName: string) => {
    if (multilanguage && activeLanguage) {
      return `${fieldName}_${activeLanguage}`;
    }
    return fieldName;
  };

  const handleKeywordAddition = () => {
    const trimmedKeyword = keywordInput.trim();

    if (trimmedKeyword !== "" && !keywords.includes(trimmedKeyword)) {
      addNewKeyword(trimmedKeyword);
      setKeywordInput("");

      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleKeywordAddition();
    }
  };

  return (
    <div>
      <Text textStyle="TS5" className="font-bold text-black ">
        {uploadContent("title")}
      </Text>
      <div className="mt-6">
        <label htmlFor={getFieldName("keywords")} className="text-gray ">
          {uploadContent("addKeywords")} {activeLanguage}
        </label>
        <Input
          ref={inputRef}
          value={keywordInput}
          onChange={(e) => setKeywordInput(e.target.value)}
          placeholder={uploadContent("placeholder")}
          name={getFieldName("keywords")}
          onKeyDown={handleKeyDown}
        />
      </div>
      <div className="flex flex-wrap gap-2 mt-4">
        {keywords.map((keyword) => (
          <div
            key={`keyword-${keyword}`}
            className="bg-lightGray text-gray text-sm px-3 py-1 rounded-md flex items-center gap-4"
          >
            {keyword}
            <Button
              type="button"
              variant="ghost"
              onClick={() => removeKeyword(keyword)}
              className="ml-2 text-gray hover:bg-transparent"
              aria-label={`Remove keyword ${keyword}`}
            >
              <X size={14} />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}
