import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { PaginationType } from "@/types/pagination";
import { castToCouponType } from "../utils/data-management/type-casting/coupons";
import { CouponInResponseType } from "../types/coupons";

export default async function retrieveCoupons(
  page: number,
  limit: number,
  searchedCoupons: string,
  selectedStatus: string
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const searchQuery = searchedCoupons?.trim()
      ? `&code=${searchedCoupons}`
      : "";

    const statusQuery =
      selectedStatus?.trim() !== "Tous les coupons"
        ? `&status=${
            selectedStatus === "Coupons expirés"
              ? "expired"
              : selectedStatus === "Coupons actifs"
              ? "active"
              : "inactive"
          }`
        : "";

    const endpoint = `/voucher-codes/dashboard?page=${page}&limit=${limit}${searchQuery}${statusQuery}`;

    const res = await GET(endpoint, headers);
    return {
      data: (res.data.data as CouponInResponseType[]).map((couponInResponse) =>
        castToCouponType(couponInResponse)
      ),
      // data: coupons,
      pagination: res.data.pagination as PaginationType,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveCoupons(page, limit, searchedCoupons, selectedStatus)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}

export async function retrieveCoupon(couponId: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/coupon/${couponId}`;

    const res = await GET(`${endpoint}`, headers);

    return castToCouponType(res.data as CouponInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveCoupon(couponId));

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
