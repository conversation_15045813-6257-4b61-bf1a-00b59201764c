import { POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

export default async function createHeroSectionOnServerSide(
  elementData: FormData
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/page-palette/landing-page";

    const res = await POST(
      `${process.env.BACKEND_ADDRESS}${endpoint}`,
      header,
      elementData
    );

    return res.status;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        createHeroSectionOnServerSide(elementData)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }
    throw new CustomError("Server Error!", 500);
  }
}
