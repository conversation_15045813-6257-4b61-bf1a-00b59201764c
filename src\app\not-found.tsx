import { getTranslations } from "next-intl/server";
import Image from "next/image";
import Link from "next/link";

export default async function NotFound() {
  const t = await getTranslations("errors.404");

  return (
    <div className="bg-white w-full min-h-[100vh] flex flex-col justify-center items-center sm:space-y-[70px] space-y-[40px]">
      <div className="flex flex-col items-center sm:space-y-[15px] space-y-[10px]">
        <Image
          alt="not found image"
          height={100}
          width={120}
          src="/error/hey.svg"
          className="sm:w-[168px] w-[120px]  h-[100px]"
        />
        <span className={"text-purple font-dm-sans"}>{t("title")}</span>
      </div>
      <Image
        alt="404 image"
        height={326}
        width={934}
        src="/error/404.svg"
        className="w-[80%] aspect-934/326 max-h-[280px]"
      />
      <Link
        href={"/"}
        className={
          "text-white font-dm-sans buttonAnimation bg-purple border-[3px] border-purple rounded-[46px] "
        }
      >
        <div className="relative sm:px-[34px] sm:py-[14px] px-[22px] py-[10px] sm: flex items-center space-x-[20px]">
          <span>{t("button")}</span>
        </div>
      </Link>
    </div>
  );
}
