import { useTranslations } from "next-intl";
import { Dispatch, SetStateAction, useState } from "react";
import CustomizableDialog from "@/components/customizable-dialog";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import FormSubmission from "../../../catalog/components/form-submission";
import { Label } from "@/components/ui/label";
import ListPicker from "@/components/list-picker";
import { DiscountType } from "../../types";
import { disableScrollOnNumberInput } from "@/utils/number-input";

interface Props {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  close: () => void;
  onUpload: () => void;
  onCurrencyChange: (currency: string) => void;
  isPending: boolean;
  warning: string;
  currency: string;
  discountType: DiscountType;
  setDiscountType: Dispatch<SetStateAction<DiscountType>>;
}

const discountsType: DiscountType[] = ["amount", "percentage"];

export function DiscountUpload({
  discountType,
  setDiscountType,
  ...props
}: Props) {
  const t = useTranslations("PromotionsManagement");
  const buttonsContent = useTranslations("shared.forms");
  const [value, setValue] = useState(0);

  // useEffect(() => {
  //   const defaultDiscount = props.defaultData
  //     ? props.defaultData.find(
  //         (promotion) => promotion.currency === props.currency
  //       )
  //     : null;
  //   if (defaultDiscount) setValue(defaultDiscount.discount * 100);
  //   else setValue(0);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [props.defaultData, props.currency]);
  return (
    <CustomizableDialog
      setIsOpen={props.setIsOpen}
      isOpen={props.isOpen}
      close={props.close}
      title={t("discountFormTitle")}
    >
      <FormSubmission
        cancel={buttonsContent("upload.cancel")}
        submit={buttonsContent("upload.update")}
        onCancel={props.close}
        onSubmit={props.onUpload}
        hideTopButtons
        isPending={props.isPending}
      >
        <div className="max-h-[500px] overflow-y-autod flex flex-col space-y-3">
          {/* <div className="w-full flex flex-col space-y-2">
            <Label htmlFor={"discount"}>{t("currency.label")}</Label>
            {currencies && (
              <PromotionCurrencyDropDown
                currency={currencies[0]}
                promotionCurrencyList={currencies}
                onClick={props.onCurrencyChange}
              />
            )}
          </div> */}
          <div className="w-full flex flex-col space-y-2">
            <Label htmlFor={"discount"}>
              {t("form.productsDiscountUpload.labels.discountType")}
            </Label>
            <ListPicker
              unselectedValueExistance={false}
              data={discountsType.map((discountType, idx) => ({
                name: t.raw("form.productsDiscountUpload.discountsType")[idx],
                id: discountType,
              }))}
              selectedElementId={discountType}
              onChange={(discountType) =>
                setDiscountType(discountType as DiscountType)
              }
              scrollableList={false}
            />
          </div>
          <div className="w-full flex flex-col space-y-2">
            <Label htmlFor={"discount"}>{t("discount.label")}</Label>
            {
              <WarnInput
                type="number"
                onWheel={disableScrollOnNumberInput}
                name="discount"
                id={"discount"}
                warning=""
                placeholder={discountType === "amount" ? "10" : "10%"}
                value={value}
              />
            }
          </div>
          {/* <div className="w-full flex justify-between items-center">
            <div>
              <Text textStyle="TS5" className="text-gray">
                {t.rich("discount.before", {
                  amount: (children) => (
                    <span className="text-gray font-bold">{children}</span>
                  ),
                })}
              </Text>
            </div>
            <div>
              {t.rich("discount.after", {
                amount: (children) => (
                  <span className="text-gray font-bold">{children}</span>
                ),
              })}
            </div>
          </div> */}
          <Text textStyle="TS5" className="mt-10 text-red text-start">
            {props.warning}
          </Text>
        </div>
      </FormSubmission>
    </CustomizableDialog>
  );
}
