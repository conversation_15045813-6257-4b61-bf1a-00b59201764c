import { HTMLAttributes, useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { ProductType } from "../../types/products";
import Image from "next/image";
import BigXMark from "@assets/icons/big-x-mark";
import Text from "@/styles/text-styles";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";

interface Props extends HTMLAttributes<"div"> {
  product: ProductType;
  onEdit: () => void;
  onDelete: (productId: string) => void;
}

export default function ProductContainer({ product, ...props }: Props) {
  const [productImage, setProductImage] = useState("/not-found/image.png");

  useEffect(() => {
    if (product.items[0]) setProductImage(product.items[0].image);
  }, [product]);

  return (
    <div
      className={cn(
        "group border rounded-md px-[5px] L:px-[14px] L:py-3 py-1 flex items-center justify-between space-x-5",
        props.className
      )}
    >
      <div className="flex items-center gap-3 regularL:gap-4">
        {product?.items?.[0]?.image ? (
          <Image
            alt="product-image"
            src={productImage}
            objectFit="cover"
            width={48}
            height={48}
            className="rounded-md w-12 h-12  border flex-shrink-0"
            unoptimized
            onError={() => setProductImage("/not-found/image.png")}
          />
        ) : (
          <div className="bg-gray w-12 h-12 group-hover:opacity-80 flex justify-center items-center rounded-lg">
            <BigXMark width={30} height={30} />
          </div>
        )}
        <Text
          textStyle="TS6"
          className="text-black overflow-hidden overflow-ellipsis"
        >
          {product.name}
        </Text>
      </div>

      <div className="flex L:space-x-3 space-x-5">
        <Button
          variant="outline"
          size="icon"
          onClick={() => {
            props.onEdit();
          }}
          className="h-8 w-8"
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => {
            props.onDelete(product.id);
          }}
          className="h-8 w-8 text-red hover:text-red-700"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
