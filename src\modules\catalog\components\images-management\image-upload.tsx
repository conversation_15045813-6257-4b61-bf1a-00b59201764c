import Text from "@/styles/text-styles";
import TrashBin from "@assets/icons/trash-bin";
import { useTranslations } from "next-intl";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

interface Props {
  defaultSrc?: string | null;
  name: string;
}

export default function ImageUpload({ defaultSrc, name }: Props) {
  const imageInputRef = useRef<HTMLInputElement>(null);
  const t = useTranslations("shared.forms.upload.file");
  const [preview, setPreview] = useState<string | null>(
    defaultSrc ? defaultSrc : null
  );
  const [fileInputKey, setFileInputKey] = useState(1);
  const [defaultImageIsDeleted, setDefaultImageIsDeleted] = useState(false);

  const [warning, setWarning] = useState("");

  useEffect(() => {
    if (defaultSrc) setPreview(defaultSrc);
  }, [defaultSrc]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type.startsWith("image")) {
        if (file.size <= 1 * 1024 * 1024) {
          if (preview) {
            URL.revokeObjectURL(preview);
          }
          const newPreviewUrl = URL.createObjectURL(file);
          setPreview(newPreviewUrl);
          setWarning("");
        } else {
          setWarning(t("warnings.imageSize"));
          e.target.value = "";
        }
      } else {
        setWarning(t("warnings.fileType"));
        e.target.value = "";
      }
    }
  };

  const removeImage = () => {
    if (imageInputRef.current) {
      if (defaultSrc && defaultSrc !== preview) {
        imageInputRef.current.value = "";
        setPreview(defaultSrc);
        setDefaultImageIsDeleted(false);
      } else {
        setPreview(null);
        setDefaultImageIsDeleted(true);
      }
    }
    setFileInputKey(fileInputKey + 1);
  };

  return (
    <div className="w-full flex flex-col space-y-1">
      <div>
        <Text textStyle="TS8" className="text-blue">
          Note
        </Text>
        <Text textStyle="TS8" className="text-gray">
          : {t("photoFormat")}
        </Text>
      </div>
      {warning && (
        <Text textStyle="TS7" className="py-2 text-red">
          {warning}
        </Text>
      )}
      <label
        className={`w-full rounded-sm border-dashed border-2 min-h-40 flex items-center justify-center mt-5 ${
          preview && "hidden"
        }`}
      >
        <div className="flex flex-col items-center space-y-2">
          <Text
            textStyle="TS7"
            className="py-2 px-3 border rounded-lg h-fit w-fit text-blue"
          >
            {t("button")}
          </Text>
          <Text textStyle="TS7" className="text-gray">
            {t("description")}
          </Text>
        </div>
        <input
          key={fileInputKey}
          name={name}
          ref={imageInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          className="m-[10px] hidden"
        />
      </label>
      {preview && (
        <div>
          <div className="relative group w-[200px] h-[200px] mt-4">
            <Image
              src={preview}
              unoptimized
              alt="default Preview"
              objectFit="cover"
              layout="fill"
              className="rounded shadow"
            />
            <input
              key={fileInputKey}
              name={"defaultImage"}
              readOnly
              value={preview}
              className="m-[10px] hidden"
            />
            <div
              onClick={removeImage}
              className="absolute top-[-10px] right-[-15px] bg-red text-white border-[2px] border-white rounded-full w-14 h-10 flex items-center justify-center group-hover:opacity-100 transition-opacity cursor-pointer"
            >
              <TrashBin />
            </div>
          </div>
        </div>
      )}

      {defaultImageIsDeleted ? (
        <input
          name={"deleted-image"}
          value="null"
          readOnly
          className="hidden"
        />
      ) : null}
    </div>
  );
}
