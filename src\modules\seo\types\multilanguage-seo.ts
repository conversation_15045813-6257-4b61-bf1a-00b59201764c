export type Language = "arabic" | "french" | "english";

export interface MultilanguageSeoContent {
  title?: string;
  description?: string;
  tags?: string[];
  language: string;
}

export interface MultilanguageSeoType {
  arabic: { metaTitle: string; metaDescription: string; tags: string[] };
  french: { metaTitle: string; metaDescription: string; tags: string[] };
  english: { metaTitle: string; metaDescription: string; tags: string[] };
}

export interface MultilanguageSeoInResponseType {
  content: MultilanguageSeoContent[];
}

export interface MultilanguageSeoContentPayload {
  content: MultilanguageSeoContent[];
}
