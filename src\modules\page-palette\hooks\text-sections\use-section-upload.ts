import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { uploadSectionToServerSide } from "../../services/sections/section-upload";
import { useQueryClient } from "@tanstack/react-query";

export default function usePagePaletteSectionUpload() {
  const router = useRouter();
  const t = useTranslations("warnings");
  const queryClient = useQueryClient();

  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const [warning, setWarning] = useState("");

  const uploadSection = async ({
    id,
    title,
    description,
    descriptionIsEmpty,
  }: {
    id?: string;
    title: string;
    description: string;
    descriptionIsEmpty?: boolean;
  }) => {
    setIsPending(true);

    if (warning !== "") setWarning("");

    try {
      //data validation
      if (descriptionIsEmpty || title.trim() === "")
        throw new CustomError("Missed Data!", 400);

      await uploadSectionToServerSide({
        id,
        content: { title, description },
      });

      queryClient.invalidateQueries({
        queryKey: ["palettes"],
        exact: false,
      });

      setIsPending(false);
      setWarning("");

      return true;
    } catch (error) {
      const customError = error as CustomError;

      if (customError.status === 401)
        router.push("/"); //user is not authenticated
      else if (customError.status === 400) {
        setWarning(t("upload.missedData"));
        toast({ title: t("warning"), description: t("upload.missedData") });
      } else {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      }

      setIsPending(false);

      return false;
    }
  };

  return {
    uploadSection,
    isPending,
    warning,
  };
}
