"use client";
import React from "react";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import useAuth from "@/modules/auth/hooks/use-auth";
import { Button } from "../../../components/ui/button";
import AuthInput from "@/modules/auth/components/auth-input";

export function Auth() {
  const { submitInfo, warning, isLoading, authRef } = useAuth("signIn");
  const t = useTranslations("auth");

  return (
    <div className="font-dm-sans max-w-md w-full mx-auto rounded-none md:rounded-2xl p-4 md:p-8 shadow-input bg-white">
      <h2 className="font-bold text-xl text-purple">{t("title")}</h2>
      <p className="text-purple text-sm max-w-sm mt-2">{t("description")}</p>
      {warning.generalWarning !== "" ? (
        <p className="text-[#EF0000] text-sm max-w-sm mt-2">
          {warning.generalWarning}
        </p>
      ) : null}

      <form
        ref={authRef}
        className="my-8 flex flex-col space-y-5"
        onSubmit={submitInfo}
      >
        <AuthInput
          useGeneralWarning={false}
          warning={warning}
          authType={"signIn"}
        />

        <Button
          className={cn(
            "group/btn bg-purple hover:bg-purple block w-full text-white rounded-md h-10 font-medium shadow-[0px_1px_0px_0px_#ffffff40_inset,0px_-1px_0px_0px_#ffffff40_inset] dark:shadow-[0px_1px_0px_0px_var(--purple)_inset,0px_-1px_0px_0px_var(--purple)_inset] active:opacity-80",
            {
              "opacity-80": isLoading,
            }
          )}
          type="submit"
          disabled={isLoading}
          id="submit-button"
        >
          {t("button")} &rarr;
        </Button>
      </form>
    </div>
  );
}

const BottomGradient = () => {
  return (
    <>
      <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-purple to-transparent" />
      <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-purple to-transparent" />
    </>
  );
};

const LabelInputContainer = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex flex-col space-y-2 w-full", className)}>
      {children}
    </div>
  );
};
