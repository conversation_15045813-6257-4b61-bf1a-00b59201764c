import {
  DropdownMenuContent,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import Text from "@/styles/text-styles";
import { Check, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface Props {
  label: string;
  list: { name: string; value: string }[];
  selectedElement: string;
  setPickedElement: (value: string) => void;
  preventFirstElementSelection?: boolean;
}

export default function ListPicker({
  label,
  selectedElement,
  list,
  setPickedElement,
  preventFirstElementSelection = false,
}: Props) {
  const pickedElement =
    selectedElement === ""
      ? { name: label, value: "" }
      : list.find((element) => element.value === selectedElement);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="w-[200px] h-10 px-2 py-1  cursor-pointer outline-none border">
        <Text textStyle="TS6" className="w-full flex justify-between">
          <span>{pickedElement ? pickedElement.name : list[0]?.name}</span>
          <ChevronDown size={20} />
        </Text>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-[200px]">
        {list.map((element, idx) =>
          preventFirstElementSelection && idx === 0 ? null : (
            <DropdownMenuItem
              key={idx}
              className={cn(
                "py-3 bg-white hover:bg-white text-black cursor-pointer flex justify-between space-x-2"
              )}
              onClick={() => {
                setPickedElement(element.value);
              }}
            >
              <Text textStyle="TS7">{element.name}</Text>
              {pickedElement?.value === element.value && <Check />}
            </DropdownMenuItem>
          )
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
