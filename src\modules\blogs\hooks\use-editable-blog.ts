import useUser from "@/modules/auth/hooks/use-user";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { retrieveEditableBlogFromServerSide } from "../services/editable-blog-extraction";
import { BlogType } from "../types/blogs";

interface Params {
  blogSlug: string;
}

export default function useEditableBlog({ blogSlug }: Params) {
  const { user } = useUser();
  const pathname = usePathname();
  const { data, isLoading, isError, refetch } = useQuery<BlogType>({
    queryKey: [blogSlug, user, pathname],
    queryFn: () => retrieveEditableBlogFromServerSide(blogSlug),
    placeholderData: keepPreviousData,
    enabled: user !== null,
  });
  return {
    isLoading,
    isError,
    refetch,
    blog: data as BlogType,
  };
}
