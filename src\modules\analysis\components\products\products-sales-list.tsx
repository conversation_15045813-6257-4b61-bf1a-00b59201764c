"use client";
import Dashboard<PERSON><PERSON><PERSON>ontainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import useProductsSales from "../../hooks/use-products-sales";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import { cn } from "@/lib/utils";
import ProductSalesContainer from "./container/sales";

export default function ProductsSalesList() {
  const t = useTranslations("dashboard.productsAnalysis");
  const { products, productsAreLoading, error } = useProductsSales(5);

  return error?.status === 403 ? null : !(
      productsAreLoading || products === undefined
    ) ? (
    <DashboardListsContainer title={t("title")}>
      {products && products.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow className="w-full bg-[#F7F9FC] hover:bg-[#F7F9FC]">
              {t.raw("productInfo").map((label: string, idx: number) => (
                <TableCell key={idx} className="text-[#6F7182] py-5">
                  <Text
                    textStyle="TS7"
                    className={cn({ "flex-1 flex justify-center": idx !== 0 })}
                  >
                    {label}
                  </Text>
                </TableCell>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody className="min-w-full text-[#6F7182]">
            {products.map((product, idx) => (
              <ProductSalesContainer product={product} key={idx} />
            ))}
          </TableBody>
        </Table>
      ) : (
        <NoDataFound className="py-5 flex justify-center items-center" />
      )}
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 5 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
