import { getGeneralWarning } from "@auth/utils/warnings/general-warning";
import {
  getSignInWarnings,
  getSignUpWarnings,
} from "@auth/utils/warnings/input-warning";
import { getSignUpFormSchema } from "../schemas/auth/sign-up";
import { getSignInFormSchema } from "../schemas/auth/sign-in";

type ResponseType = {
  ok: boolean;
  warning: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    generalWarning: string;
  };
  data: {
    email: string;
    firstName?: string;
    lastName?: string;
    password: string;
  };
};

export function verifyAuthContent(
  auth: "signUp" | "signIn",
  formRef: HTMLFormElement,
  t: (key: string) => string
): ResponseType {
  const authData = new FormData(formRef);

  const email = (authData.get("email") ? authData.get("email") : "") as string;
  const firstName = (
    authData.get("firstName") ? authData.get("firstName") : ""
  ) as string;
  const lastName = (
    authData.get("lastName") ? authData.get("lastName") : ""
  ) as string;
  const password = (
    authData.get("password") ? authData.get("password") : ""
  ) as string;

  const userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    password: string;
  } = {
    email,
    password,
  };

  if (auth === "signUp") {
    userData.firstName = firstName;
    userData.lastName = lastName;

    const signUpSchema = getSignUpFormSchema();
    const signUpVerification = signUpSchema.safeParse(userData);

    if (!signUpVerification.success) {
      const warningResult = getSignUpWarnings(signUpVerification);

      return {
        ok: false,
        warning: {
          ...warningResult,
          generalWarning: getGeneralWarning(warningResult, t),
        },
        data: userData,
      };
    }
  } else {
    const signInSchema = getSignInFormSchema();

    const signInVerification = signInSchema.safeParse(userData);

    if (!signInVerification.success) {
      const warningResult = getSignInWarnings(signInVerification);

      return {
        ok: false,
        warning: {
          ...warningResult,
          firstName: "",
          lastName: "",
          generalWarning: getGeneralWarning(warningResult, t),
        },
        data: userData,
      };
    }
  }

  return {
    ok: true,
    warning: {
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      generalWarning: "",
    },
    data: userData,
  };
}
