import ListPicker from "@/components/list-picker";
import { useTranslations } from "next-intl";

interface Props {
  onChange: (id: string) => void;
  selectedDate: string;
}

export default function PeriodFilter(props: Props) {
  const months = [
    { name: "1 Month", id: "1" },
    { name: "2 Months", id: "2" },
    { name: "3 Months", id: "3" },
    { name: "4 Months", id: "4" },
    { name: "5 Months", id: "5" },
    { name: "6 Months", id: "6" },
  ];
  const t = useTranslations("dashboard.filter");

  return (
    <div className="w-fit S:min-w-[150px]">
      <ListPicker
        label={t("period")}
        onChange={props.onChange}
        data={months}
        selectedElementId={props.selectedDate}
        unselectedValueExistance={false}
      />
    </div>
  );
}
