"use client";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import { PlusIcon, FileText } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { useState } from "react";
import usePagePaletteSectionDeletion from "../../hooks/text-sections/use-section-deletion";
import usePagePaletteSectionUpload from "../../hooks/text-sections/use-section-upload";
import { Button } from "@/components/ui/button";
import usePagePalette from "../../hooks/use-page-palette";
import PageSectionContentUpload from "./section-upload";

export default function PagePaletteSectionsManagement() {
  const { palettes, palettesAreLoading } = usePagePalette();
  const t = useTranslations("PagePalette.ContentManagement");

  const [isThereNewSection, setIsThereNewSection] = useState(false);
  const {
    isPending: deletionIsPending,
    warning: deletionWarning,
    deleteSection,
  } = usePagePaletteSectionDeletion();
  const { warning: uploadWarning, uploadSection } =
    usePagePaletteSectionUpload();

  // Check if there are no sections
  const noSectionsFound =
    !isThereNewSection &&
    (!palettes?.sections || palettes.sections.length === 0);

  return (
    <DashboardListsContainer title={t("title")}>
      {!(palettesAreLoading || palettes === undefined) ? (
        <div className="flex flex-col space-y-9 mb-5">
          <div className="w-full flex regularL:flex-row flex-col gap-2 justify-end mt-3">
            <Button
              onClick={() => setIsThereNewSection(true)}
              className="bg-[#F2F2F2] text-black hover:bg-[#F2F2F2] hover:text-black self-end regularL:order-2 order-1 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2"
              id="add-palette-button"
            >
              <PlusIcon />
              <Text textStyle="TS7">{t("add")}</Text>
            </Button>
          </div>

          <div className="flex flex-wrap gap-5">
            <div className="flex flex-col gap-5 w-full">
              {/* No sections message */}
              {noSectionsFound && (
                <div className="w-full flex flex-col items-center justify-center p-10 border border-dashed border-gray-300 rounded-lg bg-gray-50">
                  <FileText className="h-12 w-12 text-gray-400 mb-3" />
                  <Text
                    textStyle="TS5"
                    className="text-gray-500 font-medium mb-2"
                  >
                    {t("noSectionsFound.title")}
                  </Text>
                  <Text
                    textStyle="TS7"
                    className="text-gray-400 text-center max-w-md mb-4"
                  >
                    {t("noSectionsFound.description")}
                  </Text>
                  <Button
                    onClick={() => setIsThereNewSection(true)}
                    className="self-center bg-[#F2F2F2] text-black hover:bg-[#F2F2F2] hover:text-black regularL:order-2 order-1 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2"
                  >
                    <PlusIcon size={16} />
                    <Text textStyle="TS7">
                      {t("noSectionsFound.addFirstSection")}
                    </Text>
                  </Button>
                </div>
              )}

              {isThereNewSection && (
                <PageSectionContentUpload
                  index={0}
                  onSave={uploadSection}
                  onDelete={async () => {
                    setIsThereNewSection(false);
                    return true;
                  }}
                  uploadWarning={uploadWarning}
                  onSuccess={() => setIsThereNewSection(false)}
                />
              )}
              {palettes?.sections &&
                palettes.sections.map((section, idx) => (
                  <PageSectionContentUpload
                    key={section.id}
                    index={isThereNewSection ? idx + 1 : idx}
                    initialContent={{
                      title: section.title,
                      description: section.description,
                      id: section.id,
                    }}
                    onSave={uploadSection}
                    onDelete={deleteSection}
                    deletionIsPending={deletionIsPending}
                    deletionWarning={deletionWarning}
                    uploadWarning={uploadWarning}
                  />
                ))}
            </div>
          </div>
        </div>
      ) : (
        <DashboardListsContainerSkeleton>
          <div className="flex flex-col space-y-6 w-full">
            <div className="w-full flex justify-end">
              <Skeleton className="h-10 w-28 rounded-[20px]" />
            </div>

            {/* Section skeletons - matching the actual section layout */}
            {Array.from({ length: 3 }).map((_, idx) => (
              <div
                key={idx}
                className="w-full flex flex-col space-y-4 p-5 border border-gray-200 rounded-lg bg-gray-50"
              >
                {/* Section header with title and buttons */}
                <div className="w-full flex justify-between items-center border-b border-gray-200 pb-3">
                  <Skeleton className="h-7 w-32 rounded-md" />{" "}
                  {/* Section title */}
                  <div className="flex gap-2">
                    <Skeleton className="h-9 w-20 rounded-[20px]" />{" "}
                    {/* Save button */}
                    <Skeleton className="h-9 w-12 rounded-[20px]" />{" "}
                    {/* Delete button */}
                  </div>
                </div>

                {/* Section title input */}
                <div className="mb-4">
                  <Skeleton className="h-5 w-28 mb-2 rounded-md" />{" "}
                  {/* Label */}
                  <Skeleton className="h-10 w-full rounded-md" /> {/* Input */}
                  <Skeleton className="h-4 w-3/4 mt-1 rounded-md" />{" "}
                  {/* Note */}
                </div>

                {/* Section content editor */}
                <div>
                  <Skeleton className="h-5 w-32 mb-2 rounded-md" />{" "}
                  {/* Label */}
                  <Skeleton className="h-40 w-full rounded-md" />{" "}
                  {/* Text editor */}
                </div>
              </div>
            ))}
          </div>
        </DashboardListsContainerSkeleton>
      )}
    </DashboardListsContainer>
  );
}
