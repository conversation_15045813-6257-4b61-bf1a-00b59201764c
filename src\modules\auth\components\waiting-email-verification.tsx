"use client";

import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Link from "next/link";
import OutlookIcon from "@/modules/auth/assets/icons/platforms/outlook";
import GmailIcon from "@/modules/auth/assets/icons/platforms/gmail";
import YahooIcon from "@/modules/auth/assets/icons/platforms/yahoo";
import { Button } from "@/components/ui/button";
import MailIcon from "@/modules/auth/assets/icons/mail";

export default function WaitingEmailVerification() {
  const t = useTranslations("emailConfirmationPage");
  return (
    <div className="w-full flex flex-col items-center justify-center L:pt-[80px] pt-[60px] text-center">
      <div className="relative py-4 px-3 rounded-[20px] max-w-2xl w-full border border-green space-y-8">
        <div className="absolute L:top-[-60px] top-[-50px] left-1/2 transform -translate-x-1/2 L:p-5 p-3 border border-green rounded-full bg-white text-green">
          <MailIcon />
        </div>
        <div>
          <Text textStyle="TS2" className="font-bold text-[#2E3A59]">
            {t.raw("emailVerificationRequired")}
          </Text>
        </div>
        <div className="">
          <Text textStyle="TS6" className="text-gray">
            {t.raw("verificationMessage")}
          </Text>
        </div>
        <div className="flex justify-center my-8 L:space-x-8 space-x-3">
          <Link
            href="https://outlook.com"
            className="rounded-full shadow-md L:p-5 p-3"
          >
            <OutlookIcon />
          </Link>
          <Link
            href="https://gmail.com"
            className="rounded-full shadow-md L:p-5 p-3"
          >
            <GmailIcon />
          </Link>
          <Link
            href="https://yahoo.com"
            className="rounded-full shadow-md L:p-5 p-3"
          >
            <YahooIcon />
          </Link>
        </div>
        <div>
          <Button
            variant={"ghost"}
            className="px-0 text-[#069C54] font-bold text-md"
          >
            <Text textStyle="TS6">{t("resendEmail")}</Text>
          </Button>
        </div>
      </div>
    </div>
  );
}
