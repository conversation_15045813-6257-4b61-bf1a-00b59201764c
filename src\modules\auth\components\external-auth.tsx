import Text from "@/styles/text-styles";
import removeJWTTokens from "@auth/utils/jwt/remove-tokens";
import GoogleIcon from "@auth/assets/icons/social-media/google";
import { useTranslations } from "next-intl";

export default function ExternalAuth({ title }: { title: string }) {
  const t = useTranslations("auth");
  function handleGoogleAuth() {
    removeJWTTokens();

    location.href = `https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=http://${process.env.FRONTEND_DOMAIN_NAME}/google-auth&prompt=consent&response_type=code&client_id=${process.env.GOOGLE_CLIENT_ID}&scope=openid%20email%20profile&access_type=offline`;
  }

  function handleFacebookAuth() {
    removeJWTTokens();

    location.href = `https://www.facebook.com/v12.0/dialog/oauth?client_id=${process.env.FACEBOOK_CLIENT_ID}&redirect_uri=https://${process.env.FRONTEND_DOMAIN_NAME}/facebook-auth&response_type=code&scope=public_profile&state=RANDOM_STRING`;
  }

  return (
    <div className="w-full pb-2 flex flex-col text-[#888A8C] space-y-3">
      <div>
        <a
          onClick={handleGoogleAuth}
          className="cursor-pointer basis-1/2 rounded-xl pl-[13px] pr-[10px]  w-full h-[50px] bg-light-green text-black flex items-center justify-center gap-2"
        >
          <GoogleIcon />
          <Text textStyle="TS7">
            {t.rich("google", {
              span: (children) => <span className="font-bold">{children}</span>,
            })}
          </Text>
        </a>
      </div>
      {/* <div>
        <a
          onClick={handleFacebookAuth}
          className="cursor-pointer basis-1/2 rounded-xl pl-[13px] pr-[10px]  w-full h-[50px] bg-light-green text-black flex items-center justify-center gap-2"
        >
          <FacebookIcon />
          <Text textStyle="TS7">
            {t.rich("facebook", {
              span: (children) => <span className="font-bold">{children}</span>,
            })}
          </Text>
        </a>
      </div> */}
    </div>
  );
}
