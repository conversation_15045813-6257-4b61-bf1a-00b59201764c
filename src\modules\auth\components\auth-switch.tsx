import AuthInput from "./auth-input";
import Text from "@/styles/text-styles";
import { Dispatch, SetStateAction } from "react";
import ExternalAuth from "./external-auth";
import useAuth from "@auth/hooks/use-auth";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { useAuthMode } from "../store/auth-mode-store";

interface AuthSectionPropsType {
  setEmail: Dispatch<SetStateAction<string>>;
}

export default function AuthSwitch({ setEmail }: AuthSectionPropsType) {
  const t = useTranslations("auth");
  const { mode: auth, setMode: setAuth } = useAuthMode();
  const { warning, submitInfo, isLoading, authRef } = useAuth(
    auth as "signIn" | "signUp",
    setEmail
  );

  return (
    <div className=" flex flex-col space-y-4">
      <div className={"w-full flex flex-col items-center"}>
        {auth == "signUp" ? (
          <>
            <Text textStyle="TS4" className="font-bold text-black">
              {t("signUp.title")}
            </Text>
            <p className={"text-gray text-sm"}>
              <Text textStyle="TS8">
                {t.rich("signUp.description", {
                  underline: (chunk) => (
                    <span
                      className="underline cursor-pointer"
                      onClick={() => setAuth("signIn")}
                    >
                      {chunk}
                    </span>
                  ),
                })}
              </Text>
            </p>
          </>
        ) : (
          <>
            <Text textStyle="TS4" className="font-bold text-black">
              {t("signIn.title")}
            </Text>
            <p className={"text-gray text-sm"}>
              <Text textStyle="TS8">
                {t.rich("signIn.description", {
                  underline: (chunk) => (
                    <span
                      className="underline cursor-pointer"
                      onClick={() => setAuth("signUp")}
                    >
                      {chunk}
                    </span>
                  ),
                })}
              </Text>
            </p>
          </>
        )}

        <AuthInput warning={warning} authType={auth as "signIn" | "signUp"} />

        {auth === "signIn" ? (
          <div className="flex justify-between w-full">
            <Button
              className="mt-4"
              variant="ghost"
              onClick={() => setAuth("signUp")}
            >
              <Text textStyle="TS7" className="text-black font-semibold">
                {t.raw("signIn.createAccount")}
              </Text>
            </Button>
            <Button
              className="mt-4"
              variant="ghost"
              onClick={() => setAuth("reset")}
            >
              <Text textStyle="TS7" className="text-gray underline">
                {t.raw("resetPassword")}
              </Text>
            </Button>
          </div>
        ) : null}

        {/* {auth === "signUp" ? (
          <>
            <label className={"mt-[10px] flex space-x-2"}>
              {" "}
              <input
                id="receiveEmailCheckBox"
                type="checkbox"
                className="accent-white h-4 w-4"
              />
              <Text textStyle="TS7" className="font-tajawal text-[#888A8C]">
                {t.raw("receiveEmails")}
              </Text>
            </label>
            <Text textStyle="TS7" className={"mt-[10px] font-tajawal"}>
              <span className="text-[#888A8C]">
                {t.raw("policies.firstSentence")}
              </span>
              <Link
                href={t.raw("policies.navigation.link")}
                className="text-white underline"
              >
                {t.raw("policies.navigation.name")}
              </Link>
              <span className="text-[#888A8C]">
                {t.raw("policies.secondSentence")}
              </span>
            </Text>
          </>
        ) : (
          <div className="w-full mt-7 flex justify-center">
            <Link href={`/auth/reset-password`}>
              <Text textStyle="TS7" className="text-red underline font-tajawal">
                {t.raw("resetPassword")}
              </Text>
            </Link>
          </div>
        )} */}
        <div className="w-full flex items-center gap-4 mt-6 mb-4">
          <span className="text-gray">Ou continuer avec</span>
          <div className="flex-1 h-[0.5px] bg-gray"></div>
        </div>
        <ExternalAuth title={t("continueWith")} />
      </div>
    </div>
  );
}
