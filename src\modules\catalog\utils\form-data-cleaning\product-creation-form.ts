export default function cleanProductCreationFormData(
  formData: FormData
): FormData {
  const filteredFormData = new FormData();
  const categoryIds: string[] = [];
  const content: Array<{
    name: string;
    description: string;
    details: string;
    language: string;
  }> = [];
  const languages = ["arabic", "french", "english"];

  const metaFieldsToExclude = [
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
    "keywords",
    "seoContent",
  ];

  formData.forEach((value, key) => {
    if (key === "displayOrder") {
      if ((value as string).trim() !== "") filteredFormData.append(key, value);
    } else if (key === "categoryIds") {
      categoryIds.push(value as string);
    } else if (metaFieldsToExclude.includes(key)) {
      // Skip meta fields that are handled by the new metaContent structure
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english") &&
      typeof value === "string" &&
      value.trim() !== ""
    ) {
      filteredFormData.append(key, value);
    }
  });

  languages.forEach((lang) => {
    const nameKey = `name_${lang}`;
    const descKey = `description_${lang}`;
    const detailsKey = `details_${lang}`;

    let nameValue = "";
    let descValue = "";
    let detailsValue = "";

    if (formData.has(nameKey)) {
      nameValue = (formData.get(nameKey) as string)?.trim() || "";
    }

    if (formData.has(descKey)) {
      descValue = (formData.get(descKey) as string)?.trim() || "";
    }

    if (formData.has(detailsKey)) {
      detailsValue = (formData.get(detailsKey) as string)?.trim() || "";
    }

    if (nameValue !== "" || descValue !== "" || detailsValue !== "") {
      content.push({
        name: nameValue,
        description: descValue,
        details: detailsValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  if (content.length > 0) {
    filteredFormData.append("content", JSON.stringify(content));
  }

  categoryIds.forEach((id) => filteredFormData.append("categoryIds", id));

  return filteredFormData;
}
