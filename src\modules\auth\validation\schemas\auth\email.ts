import { z } from "zod";

const EnglishEmailSchema = z.object({
  email: z
    .string()
    .min(1, { message: "L'adresse e-mail est requise." })
    .email("Invalid email format"),
});

const FrenchEmailSchema = z.object({
  email: z
    .string()
    .min(1, { message: "L'adresse e-mail est requise." })
    .email("Erreur d'adresse email"),
});

export function getEmailSchema() {
  return FrenchEmailSchema;
}
