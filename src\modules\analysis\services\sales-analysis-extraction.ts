import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { castToSalesAnalysis } from "../utils/data-management/types-casting/sales";
import { CustomError } from "@/utils/custom-error";

export default async function retreiveSalesAnalysis(period: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/statistics/orders/details";

    const res = await GET(`${endpoint}?period=${period}`, headers);

    return castToSalesAnalysis(res.data);
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retreiveSalesAnalysis(period));

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    throw new CustomError(
      "Error Occured!",
      axiosError.response?.status as number
    );
  }
}
