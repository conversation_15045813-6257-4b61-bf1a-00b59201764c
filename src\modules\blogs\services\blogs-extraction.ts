import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types/pagination";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { AxiosError } from "axios";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { BlogInResponseType } from "../types/blogs";
import { castToBlogType } from "../utils/data-management/types-casting/blogs";

export async function retrieveBlogsFromServerSide({
  page,
  limit,
  searchedBlogs,
}: {
  page: number;
  limit?: number;
  searchedBlogs?: string;
}) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const searchQuery = searchedBlogs?.trim() ? `&search=${searchedBlogs}` : "";

    const limitQuery = limit ? `&limit=${limit}` : "";

    const endpoint = `/blogs/dashboard?page=${page}${limitQuery}${searchQuery}`;

    const res = await GET(endpoint, headers);

    return {
      pagination: res.data.pagination as PaginationType,
      blogs: (res.data.data as BlogInResponseType[]).map((blogInResponse) =>
        castToBlogType(blogInResponse)
      ),
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveBlogsFromServerSide({ page, limit, searchedBlogs })
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
