import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUp } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

interface Props {
  data: { name: string; id: string }[];
  name: string;
  defaultSelectedElements?: string[];
  values?: string[];
  setValues?: (values: string[]) => void;
  selectAllButtonIsUsed?: boolean;
}

export default function ElementsSelection({
  data,
  name,
  defaultSelectedElements,
  values,
  setValues,
  selectAllButtonIsUsed = false,
}: Props) {
  const t = useTranslations("shared.pickers");

  const [internalSelectedElementsId, setInternalSelectedElementsId] = useState<
    string[]
  >([]);
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [contentWidth, setContentWidth] = useState<number | null>(null);

  const selectedElementsId =
    values && setValues ? values : internalSelectedElementsId;
  const setSelectedElements =
    values && setValues
      ? (setValues as Dispatch<SetStateAction<string[]>>)
      : setInternalSelectedElementsId;

  useEffect(() => {
    if (defaultSelectedElements) {
      setSelectedElements([...defaultSelectedElements]);
    }
  }, [defaultSelectedElements, setSelectedElements]);

  const handleCheck = (checked: boolean, checkedElementId: string) => {
    const newSelectedElementsId = checked
      ? [...selectedElementsId, checkedElementId]
      : selectedElementsId.filter((id) => id !== checkedElementId);

    setSelectedElements(newSelectedElementsId);
  };

  const handleCheckAll = (checked: boolean) => {
    if (checked) setSelectedElements(data.map((element) => element.id));
    else setSelectedElements([]);
  };

  useEffect(() => {
    const handleResize = () => {
      if (triggerRef.current) {
        setContentWidth(triggerRef.current.offsetWidth);
      }
    };

    if (menuIsOpen && triggerRef.current) {
      setContentWidth(triggerRef.current.offsetWidth);
      window.addEventListener("resize", handleResize);
    } else {
      window.removeEventListener("resize", handleResize);
    }

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [menuIsOpen]);

  return (
    <div className="w-full">
      <DropdownMenu open={menuIsOpen} onOpenChange={setMenuIsOpen}>
        <DropdownMenuTrigger
          ref={triggerRef}
          className="p-3 bg-white border-[2px] w-full h-14 rounded-xl flex items-center justify-between"
        >
          <Text textStyle="TS6" className="text-black">
            {selectedElementsId.length > 0 && selectedElementsId.length < 3
              ? data
                  .filter((item) => selectedElementsId.includes(item.id))
                  .map((item) => item.name)
                  .join(", ")
              : selectedElementsId.length > 2
              ? `${selectedElementsId.length} Sélectionner`
              : "Choisir des catégories"}
          </Text>
          {menuIsOpen ? (
            <ChevronUp className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-500" />
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="border"
          style={{ width: contentWidth ? `${contentWidth}px` : "auto" }}
        >
          {selectAllButtonIsUsed && (
            <DropdownMenuCheckboxItem
              checked={selectedElementsId.length === data.length}
              onCheckedChange={(checked) => handleCheckAll(checked)}
              className={cn("bg-white text-gray L:h-10 h-7 flex items-center", {
                "font-bold": selectedElementsId.length === data.length,
              })}
            >
              {t("selectAll")}
            </DropdownMenuCheckboxItem>
          )}
          <ScrollArea className="max-h-[300px] overflow-auto">
            {data
              .sort(
                (a, b) =>
                  Number(selectedElementsId.includes(b.id)) -
                  Number(selectedElementsId.includes(a.id))
              )
              .map((element) => (
                <DropdownMenuCheckboxItem
                  key={element.id}
                  checked={selectedElementsId.includes(element.id)}
                  onCheckedChange={(checked) =>
                    handleCheck(checked, element.id)
                  }
                  className={cn(
                    "bg-white text-gray L:h-10 h-7 flex items-center",
                    {
                      "font-bold": selectedElementsId.includes(element.id),
                    }
                  )}
                >
                  {element.name}
                </DropdownMenuCheckboxItem>
              ))}
          </ScrollArea>
        </DropdownMenuContent>
      </DropdownMenu>
      {selectedElementsId.map((id) => (
        <input key={id} name={name} className="hidden" value={id} readOnly />
      ))}
    </div>
  );
}
