import { cn } from "@/lib/utils";
import { CategorySalesType } from "@/modules/analysis/types/catalog";
import { TextStyle } from "@/styles/text-styles";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Legend, Label } from "recharts";

interface Props {
  data: CategorySalesType[];
  COLORS: string[];
}

export default function CustomizedPieChart(props: Props) {
  return (
    <PieChart width={287.93} height={400}>
      {/* Pie chart */}
      <Pie
        data={props.data}
        cx="50%" // Center of the chart horizontally
        cy="50%" // Center of the chart vertically
        innerRadius={90} // Donut hole radius
        outerRadius={120} // Outer radius of the pie chart
        paddingAngle={5}
        dataKey="revenue"
        nameKey="name"
      >
        {props.data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={props.COLORS[index]} />
        ))}
        <Label
          value={"Sales"}
          cx={"50%"}
          cy={"50%"}
          position="centerTop"
          className={cn("font-bold", TextStyle["TS5"])}
        ></Label>
        <Label
          value={"2032k"}
          position="centerBottom"
          fontSize={24}
          dy={-10}
          fill="black"
          className={cn("font-bold", TextStyle["TS4"])}
        ></Label>
      </Pie>

      {/* Central label */}

      {/* Legend */}
      <Legend
        align="left"
        verticalAlign="bottom"
        layout="horizontal"
        iconType="circle"
        formatter={(value) => {
          const categorySales = props.data.find(
            (category) => category.name === value
          );
          return (
            <span style={{ color: value.color }}>
              {`${value} ${categorySales!.revenue} ${categorySales!.currency}`}
            </span>
          );
        }}
      />
    </PieChart>
  );
}
