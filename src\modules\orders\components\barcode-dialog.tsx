"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import Barcode from "react-barcode";

interface BarcodeDialogProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  barcodeValue: string;
}

export const BarcodeDialog = ({
  isOpen,
  setIsOpen,
  barcodeValue,
}: BarcodeDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle></DialogTitle>
        </DialogHeader>
        <div className="flex justify-center items-center">
          <Barcode value={barcodeValue} />
        </div>
      </DialogContent>
    </Dialog>
  );
};
