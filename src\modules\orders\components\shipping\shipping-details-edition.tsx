import Text from "@/styles/text-styles";
import { ShippingDetails } from "../../types/shipping-cost";
import { useTranslations } from "next-intl";
import WarnInput from "@/components/input/warn-input";
import useShippingDetailsEdition from "../../hooks/use-shipping-details-edition";
import FormSubmission from "@/modules/catalog/components/form-submission";
import { disableScrollOnNumberInput } from "@/utils/number-input";

interface Props {
  shipping: ShippingDetails;
  onEdited: () => void;
  close: () => void;
}

export default function ShippingDetailsEdition(props: Props) {
  const t = useTranslations("ShippingsDetails");
  const buttonsContent = useTranslations("shared.forms.upload");
  const { warning, updateShippingDetails, isPending, formRef } =
    useShippingDetailsEdition(props.onEdited);

  return (
    <FormSubmission
      cancel={buttonsContent("cancel")}
      submit={buttonsContent("create")}
      onCancel={props.close}
      onSubmit={() => {
        updateShippingDetails();
      }}
      hideTopButtons
    >
      <div className="flex justify-center">
        <Text textStyle="TS6" className="text-red-600">
          {warning}
        </Text>
      </div>
      <form ref={formRef} className="flex flex-col space-y-3">
        <input
          readOnly
          className="hidden"
          name="id"
          value={props.shipping.id}
        />
        {props.shipping.prices.map((amount, idx) => (
          <label key={idx} className="flex flex-col space-y-2">
            <Text textStyle="TS5" className="text-gray">
              {`${amount.currency} ${t("priceLabel")}`}
            </Text>
            <WarnInput
              name={amount.currency === "LYD" ? "priceInLibi" : "priceInDollar"}
              type="number"
              onWheel={disableScrollOnNumberInput}
              warning=""
              //value={amount.price}
            />
          </label>
        ))}
        <label key={"duration-key"} className="flex flex-col space-y-2">
          <Text textStyle="TS5" className="text-gray">
            {`${t("durationLabel")}`}
          </Text>
          <WarnInput
            name="duration"
            warning=""
            value={props.shipping.duration}
          />
        </label>
      </form>
    </FormSubmission>
  );
}
