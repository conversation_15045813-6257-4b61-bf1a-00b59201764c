import { SalesPerDayType } from "@/modules/analysis/types/sales";
import Text, { TextStyle } from "@/styles/text-styles";
import { Tooltip } from "@radix-ui/react-tooltip";
import {
  CartesianGrid,
  XAxis,
  YAxis,
  ResponsiveContainer,
  AreaChart,
  Area,
  Legend,
} from "recharts";

interface Props {
  data: SalesPerDayType[];
}

export default function CustomizedSimpleAreaChart(props: Props) {
  const formatPrice = (price: number) => {
    return price == 0 ? "" : `${price} ${props.data[0].currency}`; // Format as "day/month"
  };

  const XAxisTextStyle = ({ x, y, payload }: any) => (
    <text x={x} y={y} dy={16} textAnchor="middle" className={TextStyle["TS7"]}>
      {new Date(payload.value).toLocaleString("en-US", {
        month: "short",
        day: "numeric",
      })}
    </text>
  );

  const YAxisTextStyle = ({ x, y, payload }: any) => (
    <text
      x={x}
      y={y}
      dx={-25}
      dy={5}
      textAnchor="middle"
      className={TextStyle["TS7"]}
    >
      {payload.value == 0 ? "" : `${payload.value} ${props.data[0].currency}`}
    </text>
  );

  const renderCustomLegend = () => {
    return (
      <div className="pt-7 flex items-center justify-center space-x-2">
        {/* Circle shape for the legend */}
        <div className="w-3 h-3 bg-purple rounded-full"></div>
        {/* Label */}
        <Text textStyle="TS7" className="text-purple">
          Total Daily Sales
        </Text>
      </div>
    );
  };

  return (
    <ResponsiveContainer className={"min-h-[400px]"} width="100%" height="100%">
      <AreaChart
        data={props.data}
        margin={{
          top: 0,
          right: 20,
          left: 0,
          bottom: 0,
        }}
      >
        <defs>
          <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
            <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
          </linearGradient>
        </defs>
        <CartesianGrid stroke="#DCDCDC" strokeDasharray="0" vertical={false} />
        <XAxis
          dataKey="date"
          axisLine={false}
          tickFormatter={(date) =>
            new Date(date).toLocaleString("en-US", {
              month: "short",
              day: "numeric",
            })
          }
          tick={<XAxisTextStyle />}
        />
        <YAxis
          axisLine={false}
          tickFormatter={formatPrice}
          padding={{ top: 0 }}
          tick={<YAxisTextStyle />}
        />
        <Tooltip />
        <Legend align="center" content={renderCustomLegend} />
        <Area
          type="linear"
          dataKey="revenue"
          strokeLinejoin="round"
          strokeLinecap="round"
          stroke="#4000ef"
          strokeWidth={2}
          fill="url(#colorRevenue)" // Use the gradient fill
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}
