import {
  CategoryInResponseType,
  CategoryType,
} from "@/modules/catalog/types/categories";

export function castToCategoryType(
  categoryInResponse: CategoryInResponseType
): CategoryType {
  return {
    metaContent: categoryInResponse.metaContent
      ? {
          title: categoryInResponse.metaContent.title,
          description: categoryInResponse.metaContent.description,
          keywords: categoryInResponse.metaContent.tags,
        }
      : null,
    slug: categoryInResponse.slug,
    name: categoryInResponse.name,
    description: categoryInResponse.description as string,
    id: categoryInResponse.id,
    displayOrder: categoryInResponse.displayOrder,
    image: categoryInResponse.image
      ? `${process.env.BACKEND_ADDRESS}${categoryInResponse.image}`
      : null,
    subCategories: categoryInResponse.subCategories
      ? categoryInResponse.subCategories.map((subCategory) =>
          castToCategoryType(subCategory)
        )
      : [],
  };
}
