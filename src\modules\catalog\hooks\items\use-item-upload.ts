import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { validateItemData } from "../../validation/products/validate-item-data";
import uploadItemToServerSide from "../../services/item/item-upload";
import cleanItemFormData from "../../utils/form-data-cleaning/item-form";
import { useRouter } from "next/navigation";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";

interface Params {
  itemId?: string;
  productSlug: string;
}

export default function useItemUpload({ itemId, productSlug }: Params) {
  const t = useTranslations("warnings");
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [isPending, setIsPending] = useState(false);
  const [warning, setWarning] = useState("");
  const [newImages, setNewImages] = useState<File[]>([]);
  const [deletedImages, setDeletedImages] = useState<string[]>([]);

  const { toast } = useToast();

  async function submitItem(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();

    try {
      const formData = new FormData(formRef.current);
      const filteredFormData = cleanItemFormData(
        formData,
        itemId,
        newImages,
        deletedImages
      );
      validateItemData(filteredFormData);

      setWarning("");
      await uploadItemToServerSide(filteredFormData, itemId);

      if (previousUrl)
        router.replace(
          `/products/${productSlug}/edition?${getPreviousUrlQueryParam(
            previousUrl
          )}`
        );
      else router.replace(`/products/${productSlug}/edition`);
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else if (customError.message === "A product item already exists!") {
          setWarning(t("upload.productItemAlreadyExists"));
          toast({
            title: t("warning"),
            description: t("upload.productItemAlreadyExists"),
          });
        } else if (
          customError.message ===
          "This product already has product item without variation"
        ) {
          setWarning(t("upload.productItemAlreadyHasVariation"));
          toast({
            title: t("warning"),
            description: t("upload.productItemAlreadyHasVariation"),
          });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      } else if (customError.status === 404 && customError.code === "P6001") {
        setWarning(t("upload.productNotFound"));
        toast({
          title: t("warning"),
          description: t("upload.productNotFound"),
        });
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitItem,
    formRef,
    warning,
    isPending,
    setNewImages,
    setDeletedImages,
  };
}
