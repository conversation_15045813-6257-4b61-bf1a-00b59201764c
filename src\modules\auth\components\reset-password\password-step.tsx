import WarnInput from "@/components/input/warn-input";
import { useResetPasswordContext } from "@auth/context/reset-password";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";

export default function PasswordStep() {
  const t = useTranslations("shared.reset-password");

  const { passwordWarning, submitPassword, isLoading } =
    useResetPasswordContext();

  function setAuthMode(arg0: string): void {
    throw new Error("Function not implemented.");
  }

  return (
    <div className={"flex flex-col space-y-1"}>
      <Text textStyle="TS4" className="font-bold text-center text-black">
        {t.raw("title")}
      </Text>
      <Text textStyle="TS6" className="font-tajawal text-gray">
        {t.raw("passwordStep.description")}
      </Text>
      <Text textStyle="TS7" className="w-full text-red">
        {passwordWarning.generalWarning}
      </Text>
      <div className="pt-4 flex flex-col space-y-3">
        <WarnInput
          id="password"
          type="password"
          className="rounded-[10px] border border-gray text-black"
          warning={passwordWarning.password}
          placeholder={t.raw("passwordStep.password")}
        />
        <WarnInput
          id="confirmationPassword"
          type="password"
          className="rounded-[10px] border border-gray text-black"
          warning={passwordWarning.confirmationPassword}
          placeholder={t.raw("passwordStep.confirmationPassword")}
        />
        <Button
          className={cn("rounded-[15px]", { "opacity-70": isLoading })}
          disabled={isLoading}
          onClick={submitPassword}
        >
          <Text textStyle="TS7">{t.raw("passwordStep.button")}</Text>
        </Button>

        <div className="w-full flex justify-center">
          <Button variant="ghost" onClick={() => setAuthMode("signIn")}>
            <Text textStyle="TS7" className="text-gray underline">
              {t.raw("comebackButton")}
            </Text>
          </Button>
        </div>
      </div>
    </div>
  );
}
