import Text from "@/styles/text-styles";
import { useEffect, useRef, useState } from "react";
import { CategoryType } from "@/modules/catalog/types/categories";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "./ui/scroll-area";
import { ChevronDown, ChevronUp } from "lucide-react";

interface Props {
  label?: string;
  data: { name: string; id: string }[];
  selectedElementId: string;
  onChange: (value: string) => void;
  pickedElementName?: string;
  unselectedValueExistance?: boolean;
  className?: string;
  dropdownClassName?: string;
  scrollableList?: boolean;
}

export default function ListPicker({
  data,
  selectedElementId,
  onChange,
  pickedElementName,
  label = "--",
  unselectedValueExistance = true,
  className,
  dropdownClassName,
  scrollableList = true,
}: Props) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [selectedElement, setSelectedElement] = useState<
    CategoryType | { name: string; id: string } | undefined
  >();
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [contentWidth, setContentWidth] = useState<number | null>(null);

  useEffect(() => {
    setSelectedElement(
      data.find((element) => element.id === selectedElementId)
    );
  }, [selectedElementId, data]);

  useEffect(() => {
    const handleResize = () => {
      if (triggerRef.current) {
        setContentWidth(triggerRef.current.offsetWidth);
      }
    };

    if (menuIsOpen && triggerRef.current) {
      setContentWidth(triggerRef.current.offsetWidth);
      window.addEventListener("resize", handleResize);
    } else {
      window.removeEventListener("resize", handleResize);
    }

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [menuIsOpen]);

  return (
    <DropdownMenu open={menuIsOpen} onOpenChange={setMenuIsOpen}>
      <DropdownMenuTrigger
        ref={triggerRef}
        className={cn(
          "p-3 bg-white border-[2px] h-14 rounded-xl flex items-center justify-between",
          className
        )}
      >
        <Text
          textStyle="TS6"
          className="text-black overflow-hidden text-ellipsis whitespace-nowrap"
        >
          {selectedElement?.name ? selectedElement?.name : label}
        </Text>
        {menuIsOpen ? (
          <ChevronUp size={20} color={"gray"} />
        ) : (
          <ChevronDown size={20} color={"gray"} />
        )}
      </DropdownMenuTrigger>
      {pickedElementName && (
        <input
          name={pickedElementName}
          className="hidden appearance-none"
          value={selectedElementId}
          readOnly
        />
      )}
      <DropdownMenuContent
        className={cn("max-h-[300px]", dropdownClassName)}
        style={{ width: contentWidth ? `${contentWidth}px` : "auto" }}
      >
        <div className={cn({ "h-[300px]": scrollableList })}>
          <ScrollArea className="h-full">
            {unselectedValueExistance && (
              <DropdownMenuItem
                className={cn(
                  "bg-white text-gray L:h-10 h-7 flex items-center",
                  {
                    "font-bold": "" === selectedElementId,
                  }
                )}
                onClick={() => {
                  onChange("");
                  setMenuIsOpen(false);
                }}
              >
                <Text textStyle="TS7">--</Text>
              </DropdownMenuItem>
            )}
            {data.map((element, idx) => (
              <DropdownMenuItem
                key={idx}
                onClick={() => {
                  onChange(element.id);
                  setMenuIsOpen(false);
                }}
                className={cn(
                  "bg-white text-gray L:h-10 h-7 flex items-center",
                  {
                    "font-bold text-black": element.id === selectedElementId,
                  }
                )}
              >
                <Text textStyle="TS7">{element.name}</Text>
              </DropdownMenuItem>
            ))}
          </ScrollArea>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
