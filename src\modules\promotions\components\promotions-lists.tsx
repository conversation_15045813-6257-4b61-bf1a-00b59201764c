"use client";
import { useState } from "react";
import PromotionContainer from "./promotion-container";
import { PromotionUpload } from "./promotion-upload";
import { PlusIcon } from "lucide-react";
import Text from "@/styles/text-styles";
import NoDataFound from "@/components/no-data-found";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import usePromotionsDeletion from "../hooks/use-promotions-deletion";
import usePromotions from "../hooks/use-promotions";
import { AlertDialog } from "@radix-ui/react-alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useRouter } from "next/navigation";
import DashboardListsContainer from "@/components/dashbord-lists-container";
import { Button } from "@/components/ui/button";

export function PromotionsList() {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const t = useTranslations("PromotionsManagement");
  const {
    isPending,
    deletePromotion,
    alertModalIsOpen,
    cancelDeletion,
    requestPromotionDeletion,
  } = usePromotionsDeletion();
  const router = useRouter();
  const { promotions } = usePromotions();

  const handleOpenUpload = () => {
    setIsPopupOpen(true);
  };
  const handleCloseUpload = () => {
    setIsPopupOpen(false);
  };

  return promotions ? (
    <DashboardListsContainer title={t("title")}>
      {
        <div className="w-full pt-3 flex space-x-3 justify-end">
          {/* {promotions.length > 0 && (
            <button
              onClick={onDelete}
              className="bg-red text-white self-end regularL:order-1 order-2 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
            >
              <TrashIcon />
              <Text textStyle="TS7">{t("delete")}</Text>
            </button>
          )} */}
          <Button
            onClick={() => handleOpenUpload()}
            className="bg-[#F2F2F2] hover:bg-[#F2F2F2]/40 hover:text-black text-black self-end regularL:order-2 order-1 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-500"
            id="add-promotion-button"
          >
            <PlusIcon />
            <Text textStyle="TS7">{t("add")}</Text>
          </Button>
        </div>
      }
      <div className="flex flex-wrap gap-5 mt-5">
        {promotions && promotions.length > 0 ? (
          promotions.map((promotion) => (
            <PromotionContainer
              key={promotion.id}
              promotion={promotion}
              onView={() => router.push(`/promotions/${promotion.slug}`)}
              onDelete={() => requestPromotionDeletion(promotion.id)}
            />
          ))
        ) : (
          <NoDataFound className="py-5 w-full min-h-48 flex justify-center items-center" />
        )}
        <PromotionUpload isOpen={isPopupOpen} onClose={handleCloseUpload} />
      </div>
      <AlertDialog open={alertModalIsOpen}>
        <ModalDialog
          title={t("dialog.title")}
          details={t("dialog.details")}
          cancel={t("dialog.cancel")}
          confirm={t("dialog.confirm")}
          theme="red"
          isPending={isPending}
          onCancel={cancelDeletion}
          onConfirm={deletePromotion}
        />
      </AlertDialog>
    </DashboardListsContainer>
  ) : (
    <div className="w-full flex flex-col space-y-3">
      <div className="w-full flex justify-end space-x-2">
        <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
        <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
      </div>
      <div className="flex flex-wrap L:justify-start justify-center gap-2">
        {Array.from({ length: 5 }).map((_, idx) => (
          <div className="flex flex-col space-y-3" key={idx}>
            <Skeleton className="L:h-60 h-48 L:w-60 w-48 rounded-[20px]" />
            <Skeleton className="h-5 L:w-40 w-32 rounded-[20px]" />
          </div>
        ))}
      </div>
    </div>
  );
}
