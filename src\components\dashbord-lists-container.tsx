import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import React, { HTMLAttributes } from "react";
import { Skeleton } from "./ui/skeleton";

interface Props extends HTMLAttributes<"div"> {
  title?: string;
  titleWrapper?: React.ReactNode;
  headerElement?: React.ReactNode;
  children: React.ReactNode | React.ReactNode[];
  parentClassName?: string;
  includesSearchbar?: boolean;
}

export default function DashboardListsContainer(props: Props) {
  return (
    <div
      className={cn(
        "shadow-md L:px-7 L:py-6 px-4 py-3 L:rounded-[30px] rounded-[10px] h-full bg-white border flex flex-col",
        props.parentClassName
      )}
    >
      <div
        className={cn(
          "border-b pb-7 w-full flex items-center justify-between gap-2",
          {
            "L:flex-row flex-col L:items-center items-end":
              props.includesSearchbar,
          }
        )}
      >
        {props.titleWrapper ? (
          props.titleWrapper
        ) : (
          <Text textStyle="TS5" className="font-bold L:w-fit w-full">
            {props.title}
          </Text>
        )}
        {props.headerElement ? props.headerElement : null}
      </div>
      <div className={cn("flex-1", props.className)}>{props.children}</div>
    </div>
  );
}

export function DashboardListsContainerSkeleton({
  children,
  headerElement = null,
  ...props
}: {
  children: React.ReactNode | React.ReactNode[];
  headerElement?: React.ReactNode;
} & HTMLAttributes<"div">) {
  return (
    <div
      className={cn(
        "bg-white border L:px-7 L:py-6 px-4 py-3 L:rounded-[30px] rounded-[10px] h-full flex flex-col space-y-5",
        props.className
      )}
    >
      <div className="flex space-x-3 justify-between">
        <Skeleton className="h-7 L:w-48 w-32" />
        {headerElement}
      </div>
      {children}
    </div>
  );
}
