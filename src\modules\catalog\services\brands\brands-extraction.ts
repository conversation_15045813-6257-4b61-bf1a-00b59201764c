import { GET } from "@/lib/http-methods";
import { BrandInResponseType } from "../../types/brands";
import { PaginationType } from "@/types/pagination";
import { castToBrandType } from "../../utils/data-management/types-casting/brands";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { AxiosError } from "axios";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";

export async function retrieveBrandsFromServerSide(
  {
    page,
    limit,
  }: {
    page: number;
    limit?: number;
  },
  searchedProducts: string
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const searchQuery = searchedProducts?.trim()
      ? `&search=${searchedProducts}`
      : "";

    const limitQuery = limit ? `&limit=${limit}` : "";

    const endpoint = `/brands/dashboard?page=${page}${limitQuery}${searchQuery}`;

    const res = await GET(endpoint, headers);

    return {
      pagination: res.data.pagination as PaginationType,
      brands: (res.data.data as BrandInResponseType[]).map((brandInResponse) =>
        castToBrandType(brandInResponse)
      ),
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveBrandsFromServerSide({ page, limit }, searchedProducts)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
