"use client";

import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import Searchbar from "@/components/searchbar";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import useCustomers from "../hooks/use-customers";
import CustomersTable from "./customer-table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import PaginationMangement from "@/components/pagination/pagination-management";

const filterOptions = [
  { value: "none", label: "Aucun filtre" },
  { value: "name", label: "Nom" },
  { value: "email", label: "Adresse e-mail" },
];

export default function CustomersList() {
  const {
    customers,
    customersAreLoading,
    setPage,
    pagesNumber,
    page,
    setSearchedCustomers,
    selectedFilter,
    handleFilterChange,
    searchedCustomers,
    records,
  } = useCustomers(15);

  const t = useTranslations("CustomersManagement");

  return !(customersAreLoading || customers === undefined) ? (
    <DashboardListsContainer
      title={t("title")}
      includesSearchbar={true}
      headerElement={
        <div className="flex flex-col L:flex-row justify-between items-start L:items-center gap-5">
          <Select
            value={selectedFilter ?? ""}
            onValueChange={handleFilterChange}
          >
            <SelectTrigger className="relative h-10 w-40 border rounded-md shadow-sm text-sm flex items-center justify-between px-3">
              <SelectValue placeholder="Filtrer" />
            </SelectTrigger>
            <SelectContent className="w-40 ">
              {filterOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Searchbar
            searchedContent={searchedCustomers}
            setSearchedContent={setSearchedCustomers}
          />
        </div>
      }
    >
      <div className="flex flex-col space-y-3">
        {customers && customers.length > 0 ? (
          <CustomersTable customers={customers} />
        ) : (
          <NoDataFound className="py-5 flex justify-center items-center" />
        )}
        <div className="flex justify-center pt-3 px-3">
          <PaginationMangement
            records={records}
            currentPage={page}
            pagesNumber={pagesNumber}
            changePage={setPage}
          />
        </div>
      </div>
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
