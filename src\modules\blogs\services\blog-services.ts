// Blog Upload Service
export async function uploadBlogToServerSide(data: Record<string, any>) {
  const response = await fetch("/api/blogs", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to upload blog", {
      cause: { status: response.status },
    });
  }

  return response.json();
}

// Blog Language Update Service
export async function updateBlogLanguageContent(
  formData: FormData,
  blogId: string,
  language: string
) {
  const jsonData: Record<string, any> = {};
  formData.forEach((value, key) => {
    jsonData[key] = value;
  });

  const response = await fetch(`/api/blogs/${blogId}/language/${language}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(jsonData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to update blog", {
      cause: { status: response.status },
    });
  }

  return response.json();
}

// Blog Fetch Service
export async function fetchBlogsByLanguage(language: string = "french") {
  const response = await fetch(`/api/blogs?language=${language}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to fetch blogs", {
      cause: { status: response.status },
    });
  }

  return response.json();
}

// Single Blog Fetch Service
export async function fetchBlogBySlug(
  slug: string,
  language: string = "french"
) {
  const response = await fetch(`/api/blogs/${slug}?language=${language}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to fetch blog", {
      cause: { status: response.status },
    });
  }

  return response.json();
}
