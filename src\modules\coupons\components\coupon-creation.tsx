import FormSubmission from "../../catalog/components/form-submission";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import useCouponUpload from "@/modules/coupons/hooks/use-coupon-upload";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import DiscountFixed from "@assets/icons/discount-fixed";
import DiscountPercentage from "@assets/icons/discount-percentage";
import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import { Textarea } from "@/components/ui/textarea";
import FreeShipping from "@assets/icons/free-shipping";
import { disableScrollOnNumberInput } from "@/utils/number-input";

export default function CouponCreation() {
  const {
    formRef,
    submitCoupon,
    warning,
    selectedDiscountType,
    isFreeShipping,
    isForever,
    isUnlimitedUsage,
    handleDiscountTypeChange,
    handleForeverChange,
    handleUnlimitedUsageChange,
  } = useCouponUpload();

  const router = useRouter();
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("CouponsManagement");

  return (
    <div className="flex flex-col py-3">
      <FormSubmission
        cancel={uploadContent("cancel")}
        submit={uploadContent("save")}
        onCancel={() => router.push("/coupons")}
        onSubmit={submitCoupon}
      >
        <form ref={formRef} className="flex flex-col regularL:gap-8 gap-4">
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <div>
              <Text textStyle="TS4" className="font-bold text-black">
                {t("couponInformation")}
              </Text>
              <div>
                <Text textStyle="TS7" className="text-gray">
                  {t("couponHeaderDescription")}
                </Text>
              </div>
              <Text textStyle="TS7" className="text-gray">
                {t.rich("note", {
                  b: (chunk) => <span className="font-bold">{chunk}</span>,
                })}
              </Text>
            </div>
            <div className="text-red self-center">{warning}</div>

            <div className="flex flex-col space-y-10">
              {/* Allow On Promotions */}
              <div className="flex items-center space-x-3">
                <Checkbox
                  className="p-3"
                  id="allowOnPromotions"
                  name="allowOnPromotions"
                  defaultChecked={false}
                />
                <Label htmlFor="allowOnPromotions" className="cursor-pointer">
                  <Text textStyle="TS6">{t("allowOnPromotions")}</Text>
                </Label>
              </div>

              <div className="flex flex-col regularL:flex-row regularL:gap-4 gap-3">
                {/* Coupon Code */}
                <div className="w-full regularL:w-1/2 flex flex-col space-y-2">
                  <Label htmlFor="code">
                    {t("couponCode")} {uploadContent("required")}
                  </Label>
                  <Input name="code" required placeholder="ShipFree" />
                </div>
              </div>

              {/* Coupon description */}
              <div className="w-full flex flex-col space-y-2">
                <Label htmlFor="description">{t("couponDescription")}</Label>
                <Textarea name="description" required />
              </div>

              <hr className="border-gray-300" />

              <div>
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("couponType")}
                </Text>
                <div>
                  <Text textStyle="TS7" className="text-gray">
                    {t("couponTypeDescription")} {uploadContent("required")}
                  </Text>
                </div>
              </div>

              {/* Hidden inputs for form submission */}
              <input
                type="hidden"
                name="discountType"
                value={
                  selectedDiscountType === "free" ? "" : selectedDiscountType
                }
              />
              <input
                type="hidden"
                name="freeShipping"
                value={isFreeShipping ? "true" : "false"}
              />
              <input type="hidden" name="appliesToAllProducts" value={"true"} />

              {/* Coupon Type */}
              <div className="flex flex-col XL:flex-row space-y-6 XL:space-y-0 XL:space-x-6">
                <div
                  onClick={() => handleDiscountTypeChange("amount")}
                  className={`
                    flex flex-col items-center
                    w-full XL:w-1/4
                    py-6 px-12
                    rounded-md
                    border-2
                    cursor-pointer
                    min-w-[150px]
                    ${
                      selectedDiscountType === "amount"
                        ? "border-blue border-2 text-blue"
                        : "border-gray bg-white"
                    }
                  `}
                >
                  <DiscountFixed
                    color={selectedDiscountType === "amount" ? "blue" : "gray"}
                  />
                  <span className="mt-2">{t("amount")}</span>
                </div>

                <div
                  onClick={() => handleDiscountTypeChange("percentage")}
                  className={`
                    flex flex-col items-center
                    w-full XL:w-1/4
                    py-6 px-12
                    rounded-md
                    border-2
                    cursor-pointer
                    min-w-[150px]
                    ${
                      selectedDiscountType === "percentage"
                        ? "border-blue border-2 text-blue"
                        : "border-gray bg-white"
                    }
                  `}
                >
                  <DiscountPercentage
                    color={
                      selectedDiscountType === "percentage" ? "blue" : "gray"
                    }
                  />
                  <span className="mt-2">{t("percentage")}</span>
                </div>

                <div
                  onClick={() => handleDiscountTypeChange("free")}
                  className={`
                    flex flex-col items-center
                    w-full XL:w-1/4
                    py-6 px-12
                    rounded-md
                    border-2
                    cursor-pointer
                    min-w-[150px]
                    ${
                      isFreeShipping
                        ? "border-blue border-2 text-blue"
                        : "border-gray bg-white"
                    }
                  `}
                >
                  <FreeShipping color={isFreeShipping ? "blue" : "gray"} />
                  <span className="mt-2">{t("free")}</span>
                </div>
              </div>

              {/* Values & applies to */}
              {!isFreeShipping && (
                <div className="flex flex-col regularL:flex-row regularL:gap-4 gap-3">
                  {/* Discount Value */}
                  <div className="flex-1 flex-col space-y-2">
                    <Label htmlFor="discount.value">
                      {t("discountValue")} {uploadContent("required")}
                    </Label>
                    <Input
                      name="discount.value"
                      placeholder={
                        selectedDiscountType === "percentage" ? "70%" : "7DT"
                      }
                      required={true}
                      type="number"
                      onWheel={disableScrollOnNumberInput}
                    />
                  </div>
                </div>
              )}

              {/* Duration & Usage */}
              <div className="flex flex-col space-y-4">
                {/* Duration */}
                <div className="w-full flex flex-col space-y-3">
                  <Label htmlFor="duration">
                    {t("duration")} {uploadContent("required")}
                  </Label>
                  <div className="flex flex-col extraXL:flex-row gap-6">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <Label htmlFor="period.from" className="text-sm mr-2">
                          {t("from")}:
                        </Label>
                        <Input
                          id="period.from"
                          name="period.from"
                          className={cn("block", TextStyle["TS7"])}
                          type="datetime-local"
                          disabled={isForever}
                          required={!isForever}
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <Label htmlFor="period.to" className="text-sm mr-2">
                          {t("to")}:
                        </Label>
                        <Input
                          id="period.to"
                          name="period.to"
                          className={cn("block", TextStyle["TS7"])}
                          type="datetime-local"
                          disabled={isForever}
                          required={!isForever}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <Checkbox
                      id="forever"
                      name="forever"
                      checked={isForever}
                      onCheckedChange={handleForeverChange}
                      className="p-3"
                      value="true"
                    />
                    <Label htmlFor="forever" className="cursor-pointer">
                      <Text textStyle="TS6" className="text-black">
                        {t("dontSetDuration")}
                      </Text>
                    </Label>
                  </div>
                </div>

                {/* Usage Limits */}
                <div className="w-full flex flex-col space-y-2">
                  <div className="flex gap-3">
                    <div className="flex-1 flex-col space-y-2">
                      <Label htmlFor="maxRedemptions">
                        {t("usageLimits")} {uploadContent("optional")}
                      </Label>
                      <Input
                        name="maxRedemptions"
                        type="number"
                        onWheel={disableScrollOnNumberInput}
                        placeholder={t("amountOfUses")}
                        disabled={isUnlimitedUsage}
                      />
                    </div>
                    <div className="flex-1 flex-col space-y-2">
                      <Label htmlFor="maxUsesPerUser">
                        {t("amountOfUsesPerUser")} {uploadContent("required")}
                      </Label>
                      <Input
                        name="maxUsesPerUser"
                        type="number"
                        onWheel={disableScrollOnNumberInput}
                        placeholder={t("amountOfUsesPerUser")}
                        defaultValue="1"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="unlimitedUsage"
                      name="unlimitedUsage"
                      checked={isUnlimitedUsage}
                      onCheckedChange={handleUnlimitedUsageChange}
                      className="p-3"
                      value="true"
                    />
                    <Label htmlFor="unlimitedUsage" className="cursor-pointer">
                      <Text textStyle="TS6">{t("dontLimitAmountOfUses")}</Text>
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </FormSubmission>
    </div>
  );
}
