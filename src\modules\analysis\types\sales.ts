export interface SalesPerDayType {
  revenue: number;
  currency: number;
  date: Date;
}

export interface SalesAnalysisType {
  averageRevenue: number;
  totalRevenue: number;
  totalQuantitySold: number;
  dailyRevenue: SalesPerDayType[];
}

export interface SalesPerDayInResponse {
  totalRevenue: number;
  currency: number;
  date: string;
}

export interface SalesAnalysisInResponse {
  averageRevenue: number;
  totalRevenue: number;
  totalOrders: number;
  dailyRevenue: SalesPerDayInResponse[];
}
