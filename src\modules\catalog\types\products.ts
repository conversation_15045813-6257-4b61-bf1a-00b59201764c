import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface ProductType {
  id: string;
  categoryIds: string[];
  brand: { id: string; name: string };
  name: string;
  description: string | null;
  details: string | null;
  displayOrder: number;
  metaContent: SeoMetaContentType | null;
  items: ItemType[];
  slug: string;
}

export interface ProductInResponseType {
  id: string;
  slug: string;
  categoryIds: string[];
  brand: { id: string; name: string };
  name: string;
  description: string | null;
  details: string | null;
  displayOrder: number;
  metaContent: SeoMetaContentTypeInResponse;
  items: ItemInResponseType[];
}

export interface ItemType {
  id: string;
  name: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  quantity: number;
  image: string;
  images: string[];
  prices: PriceType[];
  variations: VariationType[];
  online: boolean;
}

export interface ProductItemType extends ItemType {
  name: string;
}

export interface ItemInResponseType {
  id: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  image: string;
  images: string[];
  quantity: number;
  prices: PriceInResponseType[];
  variation?: VariationType[];
  online: boolean;
}

export interface PriceType {
  currency: string;
  realPrice: number;
  promotionalPrice: number;
}

export interface PriceInResponseType {
  regularPrice: number;
  promotionalPrice: number;
  currency: string;
}

export interface VariationType {
  name: string;
  value: string;
}

export interface ErrorDataResponse {
  message: string;
  code: string;
}
