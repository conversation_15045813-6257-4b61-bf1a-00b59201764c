import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CategoryType } from "@/modules/catalog/types/categories";
import { ChevronDown, ChevronRight, Edit, Plus, Trash2 } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

interface Props {
  category: CategoryType;
  parentCategories?: CategoryType[];
  onEdit: (category: CategoryType, parentCategories: CategoryType[]) => void;
  removeCategory: (elementId: string) => void;
  onAddSubCategory: (category: CategoryType) => void;
  categoryOrder: number; //it is variable used to indicate the order of cateogry bc we've cat subcat and subsubcat
}

export default function CategoryContainer({
  category,
  onEdit,
  removeCategory,
  parentCategories = [],
  onAddSubCategory,
  categoryOrder,
}: Props) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [categoryUploadIsOpen, setCategoryUploadIsOpen] = useState(false);
  const t = useTranslations("CategoriesManagement");
  const categoryCanHaveSubCategories = categoryOrder <= 2;

  return (
    <>
      <div
        draggable
        className="flex items-center justify-between p-2 regularL:p-4 border rounded-lg hover:bg-lightGray group"
      >
        <div className="flex items-center gap-3 regularL:gap-4  cursor-pointer">
          {categoryCanHaveSubCategories && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 border rounded-md flex-shrink-0"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          <Image
            src={category.image ? category.image : "/not-found/image.png"}
            alt="category"
            width={48}
            height={48}
            className="rounded-md border flex-shrink-0"
            unoptimized
            onClick={() => setIsExpanded(!isExpanded)}
          />
          <h3 onClick={() => setIsExpanded(!isExpanded)}>
            <Text
              textStyle="TS6"
              onClick={() => setIsExpanded(!isExpanded)}
              className="regularL:font-medium cursor-pointer w-1/2"
            >
              {category.name}
            </Text>
          </h3>
        </div>

        <div className="flex items-center gap-2 transition-opacity">
          {categoryCanHaveSubCategories && (
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => onAddSubCategory(category)}
            >
              <Plus className="h-4 w-4 mr-1" />
              <Text textStyle="TS6" className="text-sm hidden tinyL:block">
                {t("subCategoryAddition")}
              </Text>
            </Button>
          )}
          <div className="flex flex-col regularL:flex-row gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={() => onEdit(category, parentCategories)}
              className="h-8 w-8"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={() => removeCategory(category.id)}
              size="icon"
              className="h-8 w-8 text-red hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      {/* Sub-categories section */}
      {categoryCanHaveSubCategories && (
        <div
          className={cn(
            "border-t transition-all duration-300 overflow-hidden",
            isExpanded ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
          )}
        >
          {category &&
          category.subCategories &&
          category.subCategories.length > 0 ? (
            <div className="ml-5 divide-y">
              {category.subCategories.map((subCategory) => (
                <CategoryContainer
                  key={subCategory.id}
                  parentCategories={[...parentCategories, category]}
                  category={subCategory}
                  onEdit={onEdit}
                  removeCategory={removeCategory}
                  onAddSubCategory={onAddSubCategory}
                  categoryOrder={categoryOrder + 1}
                />
              ))}
            </div>
          ) : (
            <div className="p-4 w-full flex justify-center text-sm text-gray-500">
              <Text textStyle="TS7">{t("noCategoriesFound")}</Text>
            </div>
          )}
        </div>
      )}
    </>
  );
}
