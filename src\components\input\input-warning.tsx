"use client";

import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import XMarkIcon from "@assets/icons/x-mark";
import { HTMLAttributes, useState } from "react";
import Text from "@/styles/text-styles";
import { cn } from "@/lib/utils";

interface InputWarningPropsType extends HTMLAttributes<HTMLDivElement> {
  warning: string;
}

export function InputWarning({ warning, className }: InputWarningPropsType) {
  const [isOpen, setIsOpen] = useState(false);
  return warning && warning !== "" ? (
    <HoverCard open={isOpen}>
      <HoverCardTrigger
        asChild
        className={className}
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
      >
        <div className="">
          <XMarkIcon />
        </div>
      </HoverCardTrigger>
      <HoverCardContent
        className={cn("px-2 mr-[14px]", {})}
        color="#4000ef"
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
      >
        <Text textStyle="TS7" className="font-tajawal text-red">
          {warning}
        </Text>
      </HoverCardContent>
    </HoverCard>
  ) : null;
}
