import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { PointsRate } from "../types";
import retrievePointsRate from "../services/points-rate-extraction";

export default function usePointsRate() {
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<PointsRate | null>({
    queryKey: ["points-rate", user],
    queryFn: retrievePointsRate,
    enabled: user !== null,
  });

  return {
    pointsRate: data,
    isLoading,
  };
}
