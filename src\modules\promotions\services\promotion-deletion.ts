import { DELETE } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";

type Response = {
  status: number;
  ok: boolean;
  error: string;
};

export async function deletePromotionOnServerSide(
  promotionId: string
): Promise<Response> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/promotions/${promotionId}`;

    await DELETE(`${process.env.BACKEND_ADDRESS}${endpoint}`, headers);

    return { ok: true, status: 200, error: "" };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        deletePromotionOnServerSide(promotionId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
