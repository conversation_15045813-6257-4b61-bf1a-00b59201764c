"use client";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import Footer from "@/components/welcoming/footer";
import useUser from "@/modules/auth/hooks/use-user";
import AuthSection from "@/sections/home/<USER>";
import IntroSection from "@/sections/home/<USER>";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function LandingPage() {
  const { user, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (user?.isAuthenticated) router.push("/dashboard");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isLoading]);

  return !(
    isLoading ||
    user === undefined ||
    (user && user.isAuthenticated)
  ) ? (
    <div className="overflow-hidden">
      <IntroSection />
      <AuthSection />
      <Footer />
    </div>
  ) : (
    <LandingPageSkeletons />
  );
}

export function LandingPageSkeletons() {
  return (
    <div className="px-4 w-full bg-white py-4 flex flex-col ">
      <div className="px-4 pt-5">
        <div className="w-full flex items-center justify-between space-x-4">
          <Skeleton className="w-32 h-10" />
          <div className="w-full L:flex hidden justify-center space-x-4">
            {Array.from({ length: 3 }).map((_, idx) => (
              <Skeleton key={idx} className="w-32 h-6" />
            ))}
          </div>
        </div>
        <div className="L:min-h-[500px] min-h-[400px] w-full flex flex-col space-y-8 items-center justify-center">
          <Skeleton className="L:w-[300px] w-[250px] h-32" />
          <div className="flex flex-col items-center space-y-2">
            <Skeleton className="L:w-[400px] w-[260px] h-10" />
            <Skeleton className="w-40 h-10" />
          </div>
        </div>
      </div>
      <DashboardListsContainerSkeleton className="self-center L:w-[400px] w-[250px]">
        <div className="flex flex-col space-y-1">
          {Array.from({ length: 6 }).map((_, idx) => (
            <Skeleton
              key={`lines${idx}`}
              className="h-5 w-full max-w-[500px]"
            />
          ))}
        </div>
        <div className="pt-5 flex flex-col space-y-5">
          {Array.from({ length: 2 }).map((_, idx) => (
            <div key={idx} className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-9 w-full max-w-[500px]" />
            </div>
          ))}
        </div>
        <Skeleton className="h-9 w-full max-w-[500px]" />
      </DashboardListsContainerSkeleton>

      <div className="pt-16 w-full max-w-[800px] self-center flex L:flex-row flex-col items-center justify-between gap-4">
        <Skeleton className="L:w-[300px] w-[250px] h-32" />
        <div className="flex flex-col space-y-1">
          {Array.from({ length: 4 }).map((_, idx) => (
            <Skeleton key={`lines${idx}`} className="h-7 w-48" />
          ))}
        </div>
      </div>
      <div className="pt-16 w-full flex justify-start space-x-4">
        {Array.from({ length: 3 }).map((_, idx) => (
          <Skeleton key={idx} className="w-32 h-6" />
        ))}
      </div>
    </div>
  );
}
