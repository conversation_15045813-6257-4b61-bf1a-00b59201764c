import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { deleteElementtOnServerSide } from "../services/element-deletion";
import { useTranslations } from "next-intl";
import { CatalogElementType } from "../types";

export default function useElementDeletion(
  type: CatalogElementType | "promotion"
) {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const [elementId, setElementId] = useState("");
  const t = useTranslations("warnings");
  const { isPending, mutate } = useMutation<unknown, CustomError>({
    mutationFn: () => deleteElementtOnServerSide(type, elementId),
    onSuccess: () => {
      if (warning !== "") setWarning("");

      setAlertModalIsOpen(false);
      setElementId("");
      queryClient.invalidateQueries({
        queryKey: [
          type === "product"
            ? "product"
            : type === "category"
            ? "category"
            : type === "brand"
            ? "brands"
            : type === "coupon"
            ? "coupons"
            : "items",
        ],
        exact: false,
      });
    },
    onError: (error) => {
      if (error.status === 500) setWarning(t("serverError"));
    },
  });

  const onDelete = (elementId: string) => {
    setElementId(elementId);
    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setElementId("");
    setAlertModalIsOpen(false);
  };

  return {
    isPending,
    alertModalIsOpen,
    onDelete,
    deleteElement: mutate,
    cancelDeletion,
    warning,
  };
}
