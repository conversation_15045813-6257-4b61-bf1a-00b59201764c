"use client";

import WarnInput from "@/components/input/warn-input";
import ImagesUpload from "@/modules/catalog/components/images-management/images-upload";
import FormSubmission from "../../form-submission";
import { useTranslations } from "next-intl";
import { Label } from "@/components/ui/label";
import ImageUpload from "../../images-management/image-upload";
import useItemUpload from "@/modules/catalog/hooks/items/use-item-upload";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import Text from "@/styles/text-styles";
import { Switch } from "@/components/ui/switch";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";
import useEditableProduct from "@/modules/catalog/hooks/products/use-editable-product";
import { disableScrollOnNumberInput } from "@/utils/number-input";

interface Props {
  productSlug: string;
}

export default function ItemCreation({ productSlug }: Props) {
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("ItemsManagement");

  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const { product, isLoading: productIsLoading } = useEditableProduct(
    productSlug || ""
  );

  const onFinish = () => {
    if (previousUrl)
      router.replace(
        `/products/${productSlug}/edition?${getPreviousUrlQueryParam(
          previousUrl
        )}`
      );
    else router.replace(`/products/${productSlug}/edition`);
  };

  const {
    formRef,
    submitItem,
    warning,
    isPending: uploadingIsPending,
  } = useItemUpload({ productSlug: product ? product.slug : "" });

  return (
    <div className="flex flex-col py-3">
      {product && (
        <FormSubmission
          cancel={uploadContent("cancel")}
          submit={uploadContent("create")}
          onCancel={onFinish}
          onSubmit={submitItem}
          isPending={uploadingIsPending}
          hideTopButtons
        >
          <form ref={formRef}>
            <Card className="flex flex-col gap-4 px-8 py-6">
              <Text textStyle="TS5" className="font-bold text-black">
                {t("itemInfo")}
              </Text>
              <div className="text-red">{warning}</div>

              <div className="flex flex-col space-y-6">
                {/* Barcode */}
                <div className="w-full flex flex-col space-y-1">
                  <Label htmlFor="barcode">
                    {uploadContent("itemLabels.barcode")}{" "}
                    {uploadContent("required")}
                  </Label>
                  <WarnInput
                    id="barcode"
                    type="text"
                    name="barcode"
                    warning=""
                  />
                </div>

                {/* Availability Toggle */}
                <Label htmlFor="online">
                  {uploadContent("itemLabels.online")}
                </Label>
                <div className="w-full flex gap-3 ">
                  <Text textStyle="TS6" className="text-gray">
                    {uploadContent("itemLabels.true")}
                  </Text>
                  <Switch
                    id="online"
                    name="online"
                    value="true"
                    defaultChecked
                  />
                </div>

                <div className="flex flex-col 2L:flex-row gap-7">
                  {/* Quantity */}
                  <div className="flex-1 flex-col space-y-1">
                    <Label htmlFor="quantity">
                      {uploadContent("itemLabels.quantity")}
                      {uploadContent("required")}
                    </Label>
                    <WarnInput
                      id="quantity"
                      type="number"
                      onWheel={disableScrollOnNumberInput}
                      name="quantity"
                      warning=""
                    />
                  </div>

                  {/* Prices */}
                  <div className="flex-1 flex-col space-y-1">
                    <Label htmlFor="price">
                      {uploadContent("itemLabels.regularPrice")}
                      {uploadContent("required")}
                    </Label>
                    <WarnInput
                      id="price"
                      type="number"
                      onWheel={disableScrollOnNumberInput}
                      name="price"
                      warning=""
                    />
                  </div>
                </div>

                <WarnInput
                  className="hidden"
                  id="productId"
                  type="text"
                  name="productId"
                  warning=""
                  value={product.id || ""}
                />

                <hr className="border-t border-light-gray my-4" />

                <Text textStyle="TS6" className="font-bold text-black">
                  {t("itemSpecialImages")}
                </Text>

                {/* Image Upload */}
                <div className="flex flex-col space-y-1 w-full 2L:w-1/4">
                  <label className="text-gray">
                    {uploadContent("itemLabels.image")}
                  </label>
                  <ImageUpload name="image" />
                </div>

                {/* Images Upload */}
                <div className="w-full flex flex-col space-y-1">
                  <label className="text-gray">
                    {uploadContent("itemLabels.images")}
                  </label>
                  <ImagesUpload name="images" mode="creation" />
                </div>
              </div>
            </Card>
          </form>
        </FormSubmission>
      )}
    </div>
  );
}
