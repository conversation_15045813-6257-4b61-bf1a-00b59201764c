"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import TrashIcon from "@assets/icons/management/trash-icon";
import Text from "@/styles/text-styles";
import { PlusIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { useState } from "react";
import ImageUpload from "../image-upload";
import Link from "next/link";
import { HeroSectionAddition } from "./upload/addition";
import usePagePalette from "../../hooks/use-page-palette";
import useHeroSectionDeletion from "../../hooks/hero-sections/use-hero-section-deletion";

export default function HeroSections() {
  const { palettes, palettesAreLoading } = usePagePalette();
  const t = useTranslations("PagePalette.HeroSections");
  const {
    isPending,
    alertModalIsOpen,
    deletePalette,
    cancelDeletion,
    onDelete,
  } = useHeroSectionDeletion();

  const [uploadModalIsOpen, setUploadModalIsOpen] = useState(false);

  return (
    <DashboardListsContainer title={t("title")}>
      {!(palettesAreLoading || palettes === undefined) ? (
        <div className="flex flex-col space-y-9 mb-5">
          <div className="w-full flex regularL:flex-row flex-col gap-2 justify-end mt-3">
            <button
              onClick={() => setUploadModalIsOpen(true)}
              className="bg-[#F2F2F2] text-black self-end regularL:order-2 order-1 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
              id="add-palette-button"
            >
              <PlusIcon />
              <Text textStyle="TS7">{t("add")}</Text>
            </button>
          </div>

          <div className="flex flex-wrap gap-5">
            <div className="flex flex-col gap-5">
              {palettes ? (
                <>
                  <div className="relative flex flex-col gap-5">
                    <Text textStyle="TS7" className="text-gray">
                      {t(palettes.name)}
                    </Text>
                    {palettes.images.map((palette, idx) => (
                      <div key={idx} className="space-y-4">
                        {palette.link && (
                          <div>
                            <Text textStyle="TS7" className="text-gray">
                              {t("url")}
                            </Text>
                            <div>
                              <Link href={palette.link}>
                                <Text textStyle="TS6" className="text-gray">
                                  {palette.link}
                                </Text>
                              </Link>
                            </div>
                          </div>
                        )}
                        <div className="flex gap-3 ">
                          <div className="flex  extraL:!flex-row rounded-lg gap-4">
                            <ImageUpload
                              name="computerImage"
                              defaultSrc={palette.mobileImage}
                              label="PC"
                            />
                            <ImageUpload
                              name="mobileImage"
                              defaultSrc={palette.computerImage}
                              label="Mobile"
                            />
                          </div>
                          <button
                            onClick={() =>
                              onDelete({
                                computerImage: palette.computerImage,
                                mobileImage: palette.mobileImage,
                              })
                            }
                            className="h-9 bg-red text-white rounded-[20px] px-2 py-1 M:px-4 M:py-2 flex items-center gap-2 active:opacity-80 active:scale-95 duration-300"
                            id={`delete-palette-button-${idx}`}
                          >
                            <TrashIcon />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <NoDataFound className="py-5 w-full min-h-48 flex justify-center items-center" />
              )}
            </div>

            <AlertDialog open={alertModalIsOpen}>
              <ModalDialog
                title={t("dialog.title")}
                details={t("dialog.details")}
                cancel={t("dialog.cancel")}
                confirm={t("dialog.confirm")}
                theme="red"
                isPending={isPending}
                onCancel={cancelDeletion}
                onConfirm={deletePalette}
              />
            </AlertDialog>
          </div>
          {
            <HeroSectionAddition
              isOpen={uploadModalIsOpen}
              setIsOpen={setUploadModalIsOpen}
              close={() => {
                setUploadModalIsOpen(false);
              }}
            />
          }
        </div>
      ) : (
        <DashboardListsContainerSkeleton>
          <div className="w-full flex flex-col space-y-3">
            <div className="w-full flex justify-end space-x-2">
              <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
              <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
            </div>
            <div className="flex flex-wrap L:justify-start justify-center gap-2">
              {Array.from({ length: 5 }).map((_, idx) => (
                <div className="flex flex-col space-y-3" key={idx}>
                  <Skeleton className="L:h-60 h-48 L:w-60 w-48 rounded-[20px]" />
                  <Skeleton className="h-5 L:w-40 w-32 rounded-[20px]" />
                </div>
              ))}
            </div>
          </div>
        </DashboardListsContainerSkeleton>
      )}
    </DashboardListsContainer>
  );
}
