import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { castToPointsRate } from "../utils/types-casting/points-rate";

export default async function retrievePointsRate() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`/points-rate`, headers);

    return castToPointsRate(res.data);
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrievePointsRate);

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
