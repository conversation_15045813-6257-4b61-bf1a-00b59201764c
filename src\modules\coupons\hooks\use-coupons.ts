import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import { CouponType } from "../types/coupons";
import retrieveCoupons from "../services/coupons-extraction";

export default function useCoupons(limit: number) {
  const { user } = useUser();

  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination();
  const [searchedCoupons, setSearchedCoupons] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("Tous les coupons");
  const { data, isLoading, isError } = useQuery<{
    data: CouponType[];
    pagination: PaginationType;
  }>({
    queryKey: [
      "coupon",
      "coupons",
      user,
      page,
      searchedCoupons,
      selectedStatus,
    ],
    queryFn: () =>
      retrieveCoupons(page, limit, searchedCoupons, selectedStatus),
    enabled: user !== null,
    placeholderData: (prev) => prev,
  });

  useEffect(() => {
    if (data?.pagination && pagesNumber !== data?.pagination?.totalPages)
      setPagesNumber(data.pagination.totalPages);

    if (data?.data) {
      setRecords(data.data.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    setPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchedCoupons]);

  return {
    coupons: data?.data,
    couponsAreLoading: isLoading,
    couponsError: isError,
    selectedStatus,
    setSelectedStatus,
    setPage,
    page,
    pagesNumber,
    records,
    setSearchedCoupons,
    searchedCoupons,
  };
}
