import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { calculatePercentageChange } from "../utils/percentage-extraction";
import { useTranslations } from "next-intl";

interface Props {
  x: number;
  y: number;
}

export default function PercentageChange(props: Props) {
  const t = useTranslations("dashboard.quickAnalysis");

  return (
    <Text textStyle="TS7">
      <span
        className={cn("text-red", {
          "text-green group-hover:text-[#00FF19]": props.x > props.y,
        })}
      >
        {`${calculatePercentageChange(props.x, props.y).toFixed(2)}%`}
      </span>
      <span className="mx-1">{t("fromLastMonth")}</span>
    </Text>
  );
}
