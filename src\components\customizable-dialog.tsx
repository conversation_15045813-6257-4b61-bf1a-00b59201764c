import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import React, { Dispatch, HTMLAttributes, SetStateAction } from "react";
import { DialogHeader } from "./ui/dialog";
import { cn } from "@/lib/utils";
import { ScrollArea } from "./ui/scroll-area";

interface Props extends HTMLAttributes<"div"> {
  title: string;
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  close: () => void;
  children: React.ReactNode;
}

export default function CustomizableDialog(props: Props) {
  return (
    <Dialog
      open={props.isOpen}
      onOpenChange={(open) => {
        if (!open) props.close();
        else props.setIsOpen(true);
      }}
    >
      <DialogContent
        className={cn("sm:max-w-[475px] bg-LightAzure", props.className)}
      >
        <DialogHeader>
          <DialogTitle></DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[700px] h-[65vh] pe-4">
          {props.children}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
