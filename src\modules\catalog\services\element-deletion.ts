import { DELETE } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";
import { CatalogElementType } from "../types";

type Response = {
  status: number;
  ok: boolean;
  error: string;
};

export async function deleteElementtOnServerSide(
  type: CatalogElementType | "promotion",
  elementId: string
): Promise<Response> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const elementTypeOnServerSide =
      type === "product"
        ? "products"
        : type === "promotion"
        ? ""
        : type === "category"
        ? "categories"
        : type === "brand"
        ? "brands"
        : type === "coupon"
        ? "voucher-codes"
        : "products/item";
    const endpoint = `/${elementTypeOnServerSide}/${elementId}`;

    await DELETE(`${process.env.BACKEND_ADDRESS}${endpoint}`, headers);

    return { ok: true, status: 200, error: "" };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        deleteElementtOnServerSide(type, elementId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
