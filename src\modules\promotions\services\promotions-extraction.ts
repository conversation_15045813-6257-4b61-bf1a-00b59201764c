import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { castToPromotionType } from "../utils/types-casting/promotions";
import { PromotionInResponseType } from "../types/promotions";

export default async function retrievePromotions() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/promotions/dashboard";

    const res = await GET(`${endpoint}`, headers);

    return (res.data as PromotionInResponseType[]).map((promotionInResponse) =>
      castToPromotionType(promotionInResponse)
    );
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrievePromotions);

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
