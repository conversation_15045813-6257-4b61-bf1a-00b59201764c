{"name": "tawer-board", "version": "1.0.0", "private": true, "workspaces": ["src/modules/*"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@fortawesome/free-brands-svg-icons": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-fontawesome": "github:fortawesome/react-fontawesome", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "@tabler/icons-react": "^3.24.0", "@tanstack/react-query": "^5.60.5", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-list-item": "^2.11.5", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-text": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.12.0", "input-otp": "^1.4.2", "lucide-react": "^0.460.0", "next": "15.0.3", "next-intl": "^3.25.1", "react": "19.0.0-rc-66855b96-20241106", "react-barcode": "^1.6.1", "react-color": "^2.19.3", "react-day-picker": "^8.10.1", "react-dom": "19.0.0-rc-66855b96-20241106", "recharts": "^2.14.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tawer-board": "file:", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^18", "@types/react-color": "^3.0.13", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}