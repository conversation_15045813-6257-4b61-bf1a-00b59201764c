import Text from "@/styles/text-styles";

interface StatusButtonsProps {
  options: string[];
  onSelect: (value: string) => void;
  selectedValue: string;
}

function StatusButtons({
  options,
  onSelect,
  selectedValue,
}: StatusButtonsProps) {
  return (
    <div className="flex items-end">
      {options.map((option) => (
        <div key={option} className="relative">
          <button
            onClick={() => onSelect(option)}
            className={`px-4 py-2 ${
              selectedValue === option
                ? "text-blue border-b-2 border-blue"
                : "text-gray"
            }`}
          >
            <Text textStyle="TS6">{option}</Text>
          </button>
          {selectedValue === option && (
            <div className="absolute bottom-0 left-0 w-full bg-blue"></div>
          )}
        </div>
      ))}
    </div>
  );
}

export default StatusButtons;
