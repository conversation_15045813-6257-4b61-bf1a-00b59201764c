import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface PaletteType {
  id: string;
  name: string;
  images: Array<{
    computerImage: string;
    mobileImage: string;
    link: string;
  }>;
  sections: { id: string; title: string; description: string }[];
  displayOrder: number;
  metaContent: SeoMetaContentType | null;
}
export interface PaletteInResponseType {
  id: string;
  name: string;
  images: Array<{
    computerImage: string;
    mobileImage: string;
    redirectUrl: string;
  }>;
  sections: { id: string; title: string; description: string }[];
  displayOrder: number;
  metaContent: SeoMetaContentTypeInResponse;
}
