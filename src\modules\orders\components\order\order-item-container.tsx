import { Separator } from "@/components/ui/separator";
import Text from "@/styles/text-styles";
import Image from "next/image";
import { OrderItem } from "../../types/orders";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { BarcodeDialog } from "../barcode-dialog";
import useCurrency from "@/modules/catalog/hooks/use-currency";

interface Props {
  item: OrderItem;
}

export default function OrderItemContainer({ item }: Props) {
  const t = useTranslations("ordersManagement.orderInfo");

  const [productImage, setProductImage] = useState("/not-found/image.png");
  const [isBarcodeDialogOpen, setIsBarcodeDialogOpen] = useState(false);
  const { currency } = useCurrency();

  useEffect(() => {
    if (item.image) setProductImage(item.image);
  }, [item]);

  return (
    <div>
      <div className="flex gap-5 mt-5 items-start">
        <Image
          alt="Order Item image"
          src={productImage}
          width={60}
          height={60}
          onError={() => setProductImage("/not-found/image.png")}
        />
        <div className="flex flex-col w-1/2">
          <Text textStyle="TS6">{item.name}</Text>

          <div className="flex justify-between gap-5 whitespace-nowrap">
            <Text textStyle="TS6" className="text-gray">
              {`${t("labels.quantity")} ${item.quantity}`}
            </Text>
            <Text textStyle="TS6" className="text-gray">
              {`${item.price} ${currency}`}
            </Text>
          </div>
          <Button
            variant="link"
            className="text-blue px-0 py-0"
            onClick={() => setIsBarcodeDialogOpen(true)}
          >
            {t("scanner")}
          </Button>
        </div>
      </div>
      <Separator />
      <BarcodeDialog
        isOpen={isBarcodeDialogOpen}
        setIsOpen={setIsBarcodeDialogOpen}
        barcodeValue={item.barcode}
      />
    </div>
  );
}
