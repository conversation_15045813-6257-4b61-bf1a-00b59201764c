"use client";
import Navbar from "@/components/welcoming/nav-bar";
import { BackgroundBeamsWithCollision } from "@/components/ui/background-beams-with-collision";
import Text from "@/styles/text-styles";
import Image from "next/image";
import { useTranslations } from "next-intl";

export default function IntroSection() {
  const t = useTranslations("welcoming.introSection");

  return (
    <div className="">
      <div className="w-full  min-h-[721px] sm:px-9 px-4 py-4 relative bg-purpleTheme flex flex-col">
        <BackgroundBeamsWithCollision className="relative flex-1 h-full w-full bg-white rounded-[10px] overflow-hidden">
          <Image
            src="/bg-effects/gradient1.png"
            alt="Responsive image"
            className="absolute top-0 left-0 w-full h-full"
            width={1038}
            height={668}
          />
          <Image
            src="/bg-effects/gradient2.png"
            alt="Responsive image"
            className="absolute top-0 right-0 w-full h-full"
            width={1038}
            height={668}
          />

          <div className="relative z-50 min-h-[721px] w-full flex flex-col">
            <picture className="absolute sm:w-[40%] w-[50%] right-[-2.5px] bottom-[-2.5px]">
              <source
                srcSet="/containers/specific-rectangle.svg"
                media="(min-width: 668px)"
              />
              <img
                src="/containers/specific-rectangle-mobile-screen.svg"
                alt="Responsive image"
              />
            </picture>
            <Navbar />
            <div className="flex-1 relative z-50 flex justify-center items-center">
              <div className="relative z-50 lg:w-[60%] w-[80%] text-center flex flex-col space-y-10 items-center">
                <Image
                  src={"/logos/tawer-logo.svg"}
                  alt="company-logo"
                  height={111}
                  width={403}
                />
                <Text textStyle="TS2" className="text-purple">
                  {t("title")}
                </Text>
              </div>
            </div>
          </div>
        </BackgroundBeamsWithCollision>
      </div>
      <div className="py-[15px] w-full flex justify-center bg-purple">
        <Text
          textStyle="TS3"
          className={
            "font-bold text-white extraL:tracking-[20px] L:tracking-[10px] M:tracking-[4.6px] tracking-[2.6px] text-center"
          }
        >
          {t("slogan")}
        </Text>
      </div>
    </div>
  );
}
