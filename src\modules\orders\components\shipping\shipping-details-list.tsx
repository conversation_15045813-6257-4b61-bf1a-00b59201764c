"use client";
import { useTranslations } from "next-intl";
import useShippingsDetails from "../../hooks/use-shippings-details";
import ShippingDetailsContainer from "./shipping-details-container";
import { useState } from "react";
import CustomizableDialog from "@/components/customizable-dialog";
import ShippingDetailsEdition from "./shipping-details-edition";
import { ShippingDetails } from "../../types/shipping-cost";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import Searchbar from "@/components/searchbar";

export default function ShippingDetailsLists() {
  const sharedContent = useTranslations("shared.searchBar");
  const { shippings, areLoading } = useShippingsDetails();
  const t = useTranslations("ShippingsDetails");
  const [editionModalIsOpen, setEditionModalIsOpen] = useState(false);
  const [editableShippingDetails, setEditableShippingDetails] =
    useState<ShippingDetails | null>(null);
  const [searchedCity, setSearchedCity] = useState("");

  const handleEditionModalOpenning = (shipping: ShippingDetails) => {
    setEditionModalIsOpen(true);
    setEditableShippingDetails(shipping);
  };

  const handleEditionModalClosing = () => {
    setEditionModalIsOpen(false);
    setEditableShippingDetails(null);
  };

  return !(areLoading || shippings === undefined) ? (
    <DashboardListsContainer
      title={t("title")}
      headerElement={
        <Searchbar
          searchedContent={searchedCity}
          setSearchedContent={setSearchedCity}
        />
      }
      includesSearchbar
      parentClassName="border-0"
    >
      <div className="overflow-x-auto flex flex-col space-y-3">
        <div className="">
          {shippings && shippings.length > 0 ? (
            shippings.map((shipping) =>
              searchedCity.trim() === "" ||
              shipping.city.name
                .toLowerCase()
                .includes(searchedCity.trim().toLowerCase()) ? (
                <ShippingDetailsContainer
                  key={shipping.id}
                  shipping={shipping}
                  onEdit={() => {
                    handleEditionModalOpenning(shipping);
                  }}
                />
              ) : null
            )
          ) : (
            <NoDataFound className="py-5 flex justify-center items-center" />
          )}
        </div>

        <CustomizableDialog
          title={t("editionTitle")}
          isOpen={editionModalIsOpen}
          setIsOpen={setEditionModalIsOpen}
          close={handleEditionModalClosing}
        >
          {editableShippingDetails ? (
            <ShippingDetailsEdition
              shipping={editableShippingDetails}
              onEdited={handleEditionModalClosing}
              close={handleEditionModalClosing}
            />
          ) : null}
        </CustomizableDialog>
      </div>
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton
      headerElement={<Skeleton className="h-7 L:w-48 w-32" />}
    >
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
