"use client";
import { useTranslations } from "next-intl";
import TopSellingProductContainer from "./container/top-selling";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import useProductsSales from "../../hooks/use-products-sales";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";

interface Props {
  productsNumber: number;
}

export default function TopSellingProductsList(props: Props) {
  const t = useTranslations("dashboard.mostOrderedProducts");
  const { products, productsAreLoading, error } = useProductsSales(
    props.productsNumber
  );

  return error?.status === 403 ? null : !(
      productsAreLoading || products === undefined
    ) ? (
    <DashboardListsContainer title={t("title")}>
      {products && products.length > 0 ? (
        products?.map((product, idx) => (
          <TopSellingProductContainer
            key={idx}
            product={product}
            last={idx === products.length - 1}
          />
        ))
      ) : (
        <NoDataFound className="min-h-40 flex justify-center items-center" />
      )}
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 5 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
