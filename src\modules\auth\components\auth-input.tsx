import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { useTranslations } from "next-intl";
import { Label } from "@/components/ui/label";

interface AuthInputPropsType {
  authType: "signIn" | "signUp";
  warning: {
    generalWarning: string;
    email: string;
    firstName?: string;
    lastName?: string;
    password: string;
  };
  useGeneralWarning?: boolean;
}

export default function AuthInput({
  authType,
  warning,
  useGeneralWarning = true,
}: AuthInputPropsType) {
  const t = useTranslations("auth");

  return (
    <div className={"flex flex-col space-y-3 w-full text-black"}>
      {useGeneralWarning && warning.generalWarning !== "" ? (
        <Text textStyle="TS6" className="text-red">
          {warning.generalWarning}
        </Text>
      ) : null}

      {authType === "signUp" && (
        <div className="flex-1 flex space-x-2 w-full ">
          <WarnInput
            id="firstName"
            name="firstName"
            warning={warning.firstName as string}
            placeholder={t.raw("input.firstName")}
            className=" h-[50px] border border-gray rounded-xl "
          />
          <WarnInput
            id="lastName"
            name="lastName"
            warning={warning.lastName as string}
            placeholder={t.raw("input.lastName")}
            className="w-full h-[50px] border border-gray rounded-xl "
          />
        </div>
      )}

      <div className="">
        <Label htmlFor="email">{t("input.email")}</Label>
        <WarnInput
          id="email"
          name="email"
          placeholder="<EMAIL>"
          type="email"
          warning={warning.email as string}
        />
      </div>
      <div className="">
        <Label htmlFor="password">{t("input.password")}</Label>
        <WarnInput
          id="password"
          name="password"
          placeholder="••••••••"
          minLength={8}
          type="password"
          eyeColor="#4000ef"
          warning={warning.password as string}
        />
      </div>
    </div>
  );
}
