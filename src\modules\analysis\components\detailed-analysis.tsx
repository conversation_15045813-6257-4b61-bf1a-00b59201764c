"use client";

import Text from "@/styles/text-styles";
import { dateToString } from "@/utils/date";
import SalesCurve from "./sales-curve";
import ProductsSalesList from "@/modules/analysis/components/products/products-sales-list";
import CategoriesSales from "@/modules/analysis/components/categories/categories-sales";
import OrdersInfoList from "@/modules/orders/components/orders-info-list";
import TopSellingProductsList from "./products/top-selling-products-list";
import useUser from "@/modules/auth/hooks/use-user";
import { useTranslations } from "next-intl";

export default function DetailedAnalysis() {
  const { user, isLoading } = useUser();
  const t = useTranslations("dashboard");

  if (isLoading) {
    return null;
  }

  return (
    user &&
    (user.role === "Admin" ? (
      <div className="w-full flex flex-col space-y-5 pb-3">
        <div className="flex flex-col">
          <Text textStyle="TS4" className="font-bold">
            {t("title")}
          </Text>
          <Text textStyle="TS6" className="text-gray">
            {dateToString(new Date())}
          </Text>
        </div>
        <div className="flex-col space-y-4">
          <div className="flex XL:flex-row flex-col gap-4">
            <div className="XL:basis-[60%]">
              <SalesCurve />
            </div>
            <div className="XL:basis-[40%]">
              <TopSellingProductsList productsNumber={5} />
            </div>
          </div>

          <ProductsSalesList />

          <div className="flex XL:flex-row flex-col gap-4">
            <div className="basis-[30%]">
              <CategoriesSales />
            </div>
            <div className="basis-[70%]">
              <OrdersInfoList />
            </div>
          </div>
        </div>
      </div>
    ) : (
      user.role === "ContentManager" && (
        <div className="w-full flex flex-col space-y-5 pb-3">
          <div className="flex-col space-y-4">
            <div className="flex XL:flex-row flex-col gap-4">
              <div className="XL:basis-[60%]">
                <OrdersInfoList />
              </div>
              <div className="XL:basis-[40%]">
                <TopSellingProductsList productsNumber={5} />
              </div>
            </div>
          </div>
        </div>
      )
    ))
  );
}
