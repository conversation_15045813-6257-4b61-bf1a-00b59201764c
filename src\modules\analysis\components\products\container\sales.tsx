"use client";
import { ProductSalesType } from "../../../types/catalog";
import Text from "@/styles/text-styles";
import Image from "next/image";
import { useEffect, useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";

interface Props {
  product: ProductSalesType;
  last?: boolean;
}

export default function ProductSalesContainer({
  product,
  last = false,
}: Props) {
  const [productImage, setProductImage] = useState("/not-found/image.png");

  useEffect(() => {
    if (product.image) setProductImage(product.image);
  }, [product]);

  return (
    <TableRow>
      <TableCell className="font-medium flex space-x-2 items-center">
        <Image
          alt={product.name}
          src={productImage}
          width={30}
          height={30}
          unoptimized
          onError={() => setProductImage("/not-found/image.png")}
        />
        <div className="flex flex-col">
          <Text textStyle="TS7" className="font-bold text-gray">
            {product.name}
          </Text>
        </div>
      </TableCell>
      <TableCell>
        <Text textStyle="TS7" className="flex-1 flex justify-center">
          {product.quantitySold}
        </Text>
      </TableCell>
      <TableCell>
        <Text textStyle="TS7" className="flex-1 flex justify-center">
          <p className="px-3 py-2 bg-[#F7F9FC] rounded-full">{`${product.revenue} ${product.currency}`}</p>
        </Text>
      </TableCell>
      <TableCell>
        <Text textStyle="TS7" className="flex-1 flex justify-center">
          <p className="px-3 py-2 bg-[#F7F9FC] rounded-full">{`${product.availableStock}`}</p>
        </Text>
      </TableCell>
    </TableRow>
  );
}
