import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRef, useState } from "react";
import useUser from "@/modules/auth/hooks/use-user";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import editShippingDetailsOnServerSide from "../services/shipping-details-edition";
import fromFormDataToJson from "@/utils/form-data-to-json";

export default function useShippingDetailsEdition(onEdited: () => void) {
  const queryClient = useQueryClient();
  const [warning, setWarning] = useState("");
  const { user } = useUser();
  const t = useTranslations("warnings");
  const formRef = useRef<HTMLFormElement>(null);
  const { isPending, mutate } = useMutation<unknown, CustomError>({
    mutationFn: () => updateShippingDetails(),
    onSuccess: () => {
      if (warning !== "") setWarning("");

      queryClient.invalidateQueries({
        queryKey: ["shippings-details"],
      });

      onEdited();
    },
    onError: (error) => {
      if (error.status === 400) setWarning(t("upload.missedData"));
      else setWarning(t("serverError"));
    },
  });

  const updateShippingDetails = async () => {
    if (formRef.current) {
      const formData = new FormData(formRef.current);

      for (const entry of formData.values()) {
        if (entry === "") throw new CustomError("Missed Data!", 400);
      }
      const shippingDetailsId = formData.get("id") as string;
      formData.delete("id");

      const shippingDetails = fromFormDataToJson(formData);
      await editShippingDetailsOnServerSide(shippingDetailsId, shippingDetails);
    }
  };

  return {
    isPending,
    updateShippingDetails: mutate,
    warning,
    formRef,
  };
}
