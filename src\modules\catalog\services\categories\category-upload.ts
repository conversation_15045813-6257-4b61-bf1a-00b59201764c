import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import { ErrorDataResponse } from "../../types/products";

export default async function uploadCategoryToServerSide(
  categoryData: FormData,
  categoryId?: string
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!categoryId) {
      const endpoint = "/categories/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        categoryData
      );

      return res.data;
    } else {
      const endpoint = "/categories";

      await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}/${categoryId}`,
        header,
        categoryData
      );
    }

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError<ErrorDataResponse>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadCategoryToServerSide(categoryData, categoryId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else if (
      axiosError.response?.status === 400 // Check if the error code is "P1000"
    ) {
      if (axiosError.response?.data.code === "P5002") {
        throw new CustomError("categoryAlreadyExists", 400, "P5002");
      }
      throw new CustomError(
        "displayOrder must not be less than 1",
        400,
        "P1000"
      );
    }

    throw new CustomError("Server Error!", 500);
  }
}
