import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { retreiveOrders } from "../services/orders-extraction";
import useUser from "@/modules/auth/hooks/use-user";
import usePagination from "@/hooks/use-pagination";

export default function useOrders(ordersNumber?: number) {
  const { user, isLoading: userIsLoading } = useUser();
  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination();
  const [searchedOrders, setSearchedOrders] = useState("");
  const { data, isLoading, isError } = useQuery({
    queryKey: ["orders", page, searchedOrders],
    queryFn: () => retreiveOrders(page, searchedOrders, ordersNumber),
    enabled: !userIsLoading && user !== null,
  });

  //updating pages number once data is fetched
  useEffect(() => {
    if (data?.pagination && pagesNumber !== data?.pagination?.totalPages)
      setPagesNumber(data.pagination.totalPages);

    if (data?.orders) {
      setRecords(data.orders.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return {
    orders: data === undefined ? undefined : data ? data.orders : null,
    ordersAreLoading: isLoading,
    pagesNumber,
    currentPage: page,
    records,
    setSearchedOrders,
    searchedOrders,
    changePage: setPage,
  };
}
