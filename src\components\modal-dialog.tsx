import {
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";

interface Props {
  title: string;
  details: string;
  cancel: string;
  confirm: string;
  isPending: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  warning?: string;
  theme?: "red";
}

export default function ModalDialog(props: Props) {
  return (
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>{props.title}</AlertDialogTitle>
        {props.warning ? (
          <Text textStyle="TS7" className="text-red">
            {props.warning}
          </Text>
        ) : null}
        <AlertDialogDescription>{props.details}</AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel
          className={cn({
            "text-red-600 hover:text-red-600": props.theme === "red",
          })}
          onClick={props.onCancel}
        >
          {props.cancel}
        </AlertDialogCancel>
        <AlertDialogAction
          onClick={props.onConfirm}
          className={cn("hover:scale-95 duration-300", {
            "opacity-80 scale-105": props.isPending,
            "bg-red hover:bg-red text-white": props.theme === "red",
          })}
        >
          {props.confirm}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  );
}
