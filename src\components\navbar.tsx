"use client";

import Text from "@/styles/text-styles";
import User from "./user";
import { SidebarTrigger } from "./ui/sidebar";
import { useTranslations } from "next-intl";

export default function Navbar() {
  const t = useTranslations("shared.navbar");
  return (
    <div className="absolute left-0 right-0 L:px-8 M:px-2 px-1 border-b  flex items-center justify-between min-h-[76px] ">
      <div className="flex M:space-x-2 space-x-1 items-center">
        <SidebarTrigger />
        <Text textStyle="TS6" className="font-bold">
          {t("title")}
        </Text>
      </div>
      <div className="w-fit">
        <User />
      </div>
    </div>
  );
}
