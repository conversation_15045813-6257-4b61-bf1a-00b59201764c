import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import retrieveCategories from "@/modules/catalog/services/categories/categories-extraction";
import { CategoryType } from "../../types/categories";
import usePagination from "@/hooks/use-pagination";

export default function useCategories() {
  usePagination();
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<CategoryType[]>({
    queryKey: ["categories", "category", user],
    queryFn: retrieveCategories,
    enabled: user !== null,
  });

  return {
    categories: data || [],
    categoriesAreLoading: isLoading,
    categoriesError: isError,
  };
}
