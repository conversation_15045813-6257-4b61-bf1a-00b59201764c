import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import retrieveProductsSales from "../services/products-sales-extraction";
import { ProductSalesType } from "../types/catalog";
import { CustomError } from "@/utils/custom-error";

export default function useProductsSales(limit?: number) {
  const { user } = useUser();
  const [currentPage, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { data, isLoading, isError, error } = useQuery<
    {
      data: ProductSalesType[];
      pagination: PaginationType;
    },
    CustomError
  >({
    queryKey: ["products-sales", user, currentPage],
    queryFn: () => retrieveProductsSales(currentPage, limit),
    enabled: user !== null,
  });

  useEffect(() => {
    if (data && data.pagination) {
      setTotalPages(data.pagination.totalPages);
    }
  }, [data]);

  return {
    products: error ? [] : data?.data,
    productsAreLoading: isLoading,
    productsError: isError,
    changePage: setPage,
    currentPage,
    totalPages,
    error,
  };
}
