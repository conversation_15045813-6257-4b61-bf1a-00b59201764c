import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { BrandType } from "../../types/brands";
import { retrieveBrandsFromServerSide } from "../../services/brands/brands-extraction";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import useUrlParams from "@/hooks/urls-management/use-url-params";

interface Params {
  paginationAffectUrl?: boolean;
  limit?: number;
}

export default function useBrands({
  limit,
  paginationAffectUrl = false,
}: Params) {
  const [searchedBrands, setSearchedBrands] = useState("");
  const [searchVersion, setSearchVersion] = useState(0);

  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination({ paginationAffectUrl });
  const { updateUrlParams, getParamFromUrl, removeParamFromUrl } =
    useUrlParams();

  const { data, isLoading, isError } = useQuery<{
    brands: BrandType[];
    pagination: PaginationType;
  } | null>({
    queryKey: ["brands", page, searchedBrands],
    queryFn: () =>
      retrieveBrandsFromServerSide(
        {
          page,
          limit,
        },
        searchedBrands
      ),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    if (
      data &&
      data.pagination &&
      pagesNumber !== data.pagination?.totalPages
    ) {
      setPagesNumber(data.pagination.totalPages);
      setRecords(data.brands.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  //get the searched products from the url
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchedBrandsInUrl = getParamFromUrl("search");

      if (searchedBrandsInUrl) setSearchedBrands(searchedBrandsInUrl);
    }
  }, []);

  //update the url when the searched brands change
  useEffect(() => {
    if (searchedBrands !== "") {
      setPage(1);

      updateUrlParams("search", searchedBrands);
    } else {
      const searchInUrl = getParamFromUrl("search");

      if (searchInUrl) removeParamFromUrl("search");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchVersion]);

  const searchBrands = (text: string) => {
    setSearchedBrands(text);
    setSearchVersion(searchVersion + 1);
  };

  return {
    brands: data && data.brands ? data?.brands : [],
    brandsAreLoading: isLoading,
    brandsError: isError,
    setPage,
    page,
    records,
    pagesNumber,
    searchedBrands,
    searchBrands,
  };
}
