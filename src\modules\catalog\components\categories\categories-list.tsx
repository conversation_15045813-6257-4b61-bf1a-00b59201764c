"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import useElementsDeletion from "../../hooks/use-elements-deletion";
import Text from "@/styles/text-styles";
import { PlusIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import useCategories from "../../hooks/categories/use-categories";
import { CategoryType } from "../../types/categories";
import { useTranslations } from "next-intl";
import CategoryContainer from "./container/category";
import CustomizableDialog from "@/components/customizable-dialog";
import CategoryCreation from "./upload/categories/category-creation";
import CategoryEdition from "./upload/categories/category-edition";

export default function CategoriesList() {
  const t = useTranslations("CategoriesManagement");

  const [uploadingIsOpen, setUploadingIsOpen] = useState(false);

  const { categories, categoriesAreLoading } = useCategories();
  const {
    isPending,
    deleteAllElements,
    alertModalIsOpen,
    cancelDeletion,
    requireDirectDeletion,
  } = useElementsDeletion("category");

  const [
    uploadedCategoryParentCategories,
    setUploadedCategoryParentCategories,
  ] = useState<CategoryType[]>([]);
  const [uploadedCategory, setUploadedCategory] = useState<CategoryType | null>(
    null
  );

  const openCategoryEdition = ({
    category,
    parentCategories,
  }: {
    category?: CategoryType;
    parentCategories?: CategoryType[];
  }) => {
    setUploadingIsOpen(true);
    if (category) {
      setUploadedCategory(category);

      if (parentCategories) {
        setUploadedCategoryParentCategories(parentCategories);
      }
    }
  };

  const openCategoryAddition = (parentCategory?: CategoryType) => {
    setUploadingIsOpen(true);

    if (parentCategory) {
      setUploadedCategoryParentCategories([parentCategory]);
    }
  };

  const onCloseCategoryUpload = () => {
    setUploadingIsOpen(false);
    // setUploadedCategory(null);
  };

  return (
    <DashboardListsContainer title={t("title")}>
      {!categoriesAreLoading ? (
        <div className="flex flex-col space-y-5 mt-3">
          {categories && categories.length > 0 && (
            <div className="w-full flex regularL:flex-row flex-col gap-2 justify-end">
              <Button
                onClick={() => openCategoryAddition()}
                className="bg-[#F2F2F2] text-black hover:bg-[#F2F2F2] hover:text-black self-end regularL:order-2 order-1 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2"
                id="add-type-button"
              >
                <PlusIcon />
                <Text textStyle="TS7">{t("add")}</Text>
              </Button>
            </div>
          )}
          <div className="flex flex-col space-y-0">
            {categories && categories.length > 0 ? (
              categories.map((category) => (
                <CategoryContainer
                  key={category.id}
                  category={category}
                  removeCategory={requireDirectDeletion}
                  onEdit={(
                    category: CategoryType,
                    parentCategories: CategoryType[]
                  ) => openCategoryEdition({ category, parentCategories })}
                  onAddSubCategory={openCategoryAddition}
                  categoryOrder={1}
                />
              ))
            ) : (
              <NoDataFound className="py-5 w-full min-h-48 flex justify-center items-center" />
            )}

            {/* category upload */}
            <CustomizableDialog
              setIsOpen={setUploadingIsOpen}
              isOpen={uploadingIsOpen}
              close={onCloseCategoryUpload}
              title={""}
              className="sm:max-w-full w-[95%]"
            >
              <div className="max-h-[500px]">
                {uploadedCategory ? (
                  <CategoryEdition
                    onSuccess={onCloseCategoryUpload}
                    onCancel={onCloseCategoryUpload}
                    categoryParams={uploadedCategory}
                    parentCategories={uploadedCategoryParentCategories}
                  />
                ) : (
                  <CategoryCreation
                    onCancel={onCloseCategoryUpload}
                    onSuccess={onCloseCategoryUpload}
                    parentCategories={uploadedCategoryParentCategories}
                  />
                )}
              </div>
            </CustomizableDialog>

            <AlertDialog open={alertModalIsOpen}>
              <ModalDialog
                title={t("dialog.title")}
                details={t("dialog.categoryDeletionDetails")}
                cancel={t("dialog.cancel")}
                confirm={t("dialog.confirm")}
                theme="red"
                isPending={isPending}
                onCancel={cancelDeletion}
                onConfirm={deleteAllElements}
              />
            </AlertDialog>
          </div>
        </div>
      ) : (
        <DashboardListsContainerSkeleton>
          <div className="w-full flex flex-col space-y-3">
            <div className="w-full flex justify-end space-x-2">
              <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
            </div>

            <div className="flex flex-col space-y-2">
              {Array.from({ length: 10 }).map((_, idx) => (
                <div
                  key={idx}
                  className="flex items-center space-x-2 border border-lightGray p-4 rounded-2xl"
                >
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-6 w-40" />
                  </div>
                  <Skeleton className="h-8 w-48 rounded-[20px]" />
                  <Skeleton className="h-6 w-6" />
                </div>
              ))}
            </div>
          </div>
        </DashboardListsContainerSkeleton>
      )}
    </DashboardListsContainer>
  );
}
