import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { PromotionProductInResponseType } from "../../types/products";
import { PaginationType } from "@/types/pagination";
import { castToPromotionProductType } from "../../utils/types-casting/products";

interface Params {
  limit: number;
  page: number;
  searchedProducts: string;
  inPromotion: boolean;
  slug?: string;
  categoriesSlugs?: string[];
}

export default async function retrievePromotionProducts({
  limit,
  page,
  searchedProducts,
  inPromotion,
  slug,
  categoriesSlugs,
}: Params) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };
  const inPromotionParams = `promotionStatus=${inPromotion ? "in" : "out"}`;
  const categoriesParam = categoriesSlugs
    ? `categorySlugs=${categoriesSlugs}`
    : "";
  const params = `?page=${page}&limit=${limit}&search=${searchedProducts}&${inPromotionParams}&${categoriesParam}`;

  const query = `/promotions/${slug}/products/dashboard`;

  try {
    const res = await GET(`${query}${params}`, headers);

    return {
      data: (res.data.data as PromotionProductInResponseType[]).map(
        (productInResponse) => castToPromotionProductType(productInResponse)
      ),
      pagination: res.data.pagination as PaginationType,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrievePromotionProducts({
          limit,
          page,
          searchedProducts,
          inPromotion,
          slug,
          categoriesSlugs,
        })
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
