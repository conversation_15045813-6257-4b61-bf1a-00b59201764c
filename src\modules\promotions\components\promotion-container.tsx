import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import Text from "@/styles/text-styles";
import { Calendar, Clock, Eye, Infinity, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { PromotionType } from "../types/promotions";
import { getStatusColor } from "@/utils/design-system/status-color-extraction";
import { cn } from "@/lib/utils";
import { getPromotionStatus } from "../utils/promotion-status";
import { Separator } from "@/components/ui/separator";

interface Props {
  promotion: PromotionType;
  onDelete: () => void;
  onView: () => void;
}

export default function PromotionContainer({
  promotion,
  onDelete,
  onView,
}: Props) {
  const t = useTranslations("PromotionsManagement");
  const status = !promotion.forever
    ? getPromotionStatus(promotion.startTime, promotion.endTime)
    : "active";

  return (
    <Card className="w-fit max-w-[300px] overflow-hidden transition-all hover:shadow-md border-muted group">
      <div
        className={cn(
          "h-1.5 w-full",
          status === "active"
            ? "bg-green"
            : status === "upcoming"
            ? "bg-blue"
            : "bg-gray"
        )}
      />

      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <div className="flex justify-between space-x-2">
              <h3 className="text-xl font-bold tracking-tight">
                <Text textStyle="TS6">{promotion.name}</Text>
              </h3>
              <div
                className={cn(
                  "text-xs font-medium px-2 py-0.5 rounded-xl border flex items-center",
                  getStatusColor(status)
                )}
              >
                {status === "active"
                  ? t("promotionStatus.active")
                  : status === "upcoming"
                  ? t("promotionStatus.upcoming")
                  : t("promotionStatus.expired")}
              </div>
            </div>
            <Separator className="my-2" />
            <p>
              <Text textStyle="TS7">{promotion.description}</Text>
            </p>
            <Separator className="mt-2" />
          </div>
        </div>
      </CardHeader>

      <CardContent className="pb-4 space-y-3">
        {promotion.forever ? (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Infinity className="h-5 w-5 text-primary" />
            <span className="font-medium">pour toujours</span>
          </div>
        ) : (
          <>
            {
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 text-primary" />
                <Text textStyle="TS6">
                  {`${t("promotionLabels.from")}: ${new Date(
                    promotion.startTime
                  ).toLocaleString()}`}
                </Text>
              </div>
            }
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4 text-primary" />
              <Text textStyle="TS6">
                {`${t("promotionLabels.to")}: ${new Date(
                  promotion.endTime
                ).toLocaleString()}`}
              </Text>
            </div>
          </>
        )}
      </CardContent>

      <CardFooter className="flex items-center gap-2 pt-2 border-t">
        <Button
          variant="outline"
          className="flex-1 gap-2 h-10"
          onClick={onView}
        >
          <Eye className="h-4 w-4" />
          <Text textStyle="TS7">{t("view")}</Text>
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="h-10 w-10 text-destructive hover:text-destructive-foreground hover:bg-destructive"
          onClick={onDelete}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}
