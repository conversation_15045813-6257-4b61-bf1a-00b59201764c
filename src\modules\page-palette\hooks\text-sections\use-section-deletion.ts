import { useState } from "react";
import { deleteSectionOnServerSide } from "../../services/sections/section-deletion";
import { CustomError } from "@/utils/custom-error";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";

export default function usePagePaletteSectionDeletion() {
  const router = useRouter();
  const t = useTranslations("warnings");
  const queryClient = useQueryClient();

  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const [warning, setWarning] = useState("");

  const deleteSection = async (id: string) => {
    setIsPending(true);
    try {
      await deleteSectionOnServerSide({ id });

      queryClient.invalidateQueries({
        queryKey: ["palettes"],
        exact: false,
      });

      setIsPending(false);
      setWarning("");

      return true;
    } catch (error) {
      const customError = error as CustomError;

      if (customError.status === 401)
        router.push("/"); //user is not authenticated
      else {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      }

      setIsPending(false);

      return false;
    }
  };

  return {
    deleteSection,
    isPending,
    warning,
  };
}
