import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { SalesAnalysisType } from "../types/sales";
import retreiveSalesAnalysis from "../services/sales-analysis-extraction";
import { useState } from "react";
import { CustomError } from "@/utils/custom-error";

export default function useSalesAnalysis() {
  const { user } = useUser();
  const [period, setPeriod] = useState("1");
  const { data, isLoading, isError, error } = useQuery<
    SalesAnalysisType,
    CustomError
  >({
    queryKey: ["products-sales", user, period],
    queryFn: () => retreiveSalesAnalysis(`${period}Months`),
    enabled: user !== null,
    placeholderData: (prev) => prev,
  });

  return {
    sales: data,
    isLoading,
    setPeriod,
    period,
    error,
  };
}
