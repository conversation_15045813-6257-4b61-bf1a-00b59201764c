import { SeoMetaContentType } from "@/modules/seo/types";
import { CustomError } from "@/utils/custom-error";

export function validateSeoMetaData(metaContent: SeoMetaContentType): void {
  if (
    !metaContent ||
    !metaContent.title ||
    metaContent.title.trim() === "" ||
    !metaContent.description ||
    metaContent.description.trim() === ""
  ) {
    throw new CustomError(`Missed Data!`, 400);
  }
}
