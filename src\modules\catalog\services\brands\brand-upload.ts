import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

export default async function uploadBrandToServerSide(
  elementData: FormData,
  elementId?: string
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!elementId) {
      const endpoint = "/brands/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        elementData
      );

      return res.data;
    } else {
      const endpoint = "/brands";

      await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}/${elementId}`,
        header,
        elementData
      );
    }

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadBrandToServerSide(elementData, elementId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }
    throw new CustomError("Server Error!", 500);
  }
}
