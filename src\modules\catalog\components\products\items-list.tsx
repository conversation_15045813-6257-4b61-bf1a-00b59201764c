"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import useElementsDeletion from "../../hooks/use-elements-deletion";
import CatalogCotainer from "../catalog-container";
import { ItemType } from "../../types/products";
import TrashIcon from "@assets/icons/management/trash-icon";
import Text from "@/styles/text-styles";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import NoDataFound from "@/components/no-data-found";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";

interface Props {
  items: ItemType[];
  product: { slug: string; id: string };
}

export default function ItemsList({ items, product }: Props) {
  const t = useTranslations("ItemsManagement");

  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const {
    isPending,
    addElementToDeletionList,
    removeElementFromDeletionList,
    deleteAllElements,
    alertModalIsOpen,
    cancelDeletion,
    onDelete,
  } = useElementsDeletion("item", product.id);

  const onCreateItem = () => {
    if (previousUrl)
      router.push(
        `/products/${product.slug}/items/creation?${getPreviousUrlQueryParam(
          previousUrl
        )}`
      );
    else router.push(`/products/${product.slug}/items/creation`);
  };

  const onEditItem = (itemId: string) => {
    if (previousUrl)
      router.push(
        `/products/${
          product.slug
        }/items/edition?itemId=${itemId}&${getPreviousUrlQueryParam(
          previousUrl
        )}`
      );
    else
      router.push(`/products/${product.slug}/items/edition?itemId=${itemId}`);
  };

  return (
    items && (
      <div className="flex flex-col space-y-3">
        <div className="w-full flex flex-row gap-2 justify-end mt-3">
          <button
            type="button"
            onClick={onDelete}
            className="bg-red text-white self-end w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
            id="delete-brands-button"
          >
            <TrashIcon />
            <Text textStyle="TS7">{t("delete")}</Text>
          </button>
          <button
            type="button"
            onClick={onCreateItem}
            className="bg-[#F2F2F2] text-black self-end  w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
            id="add-brand-button"
          >
            <PlusIcon />
            <Text textStyle="TS7">{t("add")}</Text>
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          {items && items.length > 0 ? (
            items.map((item, idx) => (
              <CatalogCotainer
                key={idx}
                type="item"
                catalog={{
                  id: item.id,
                  name: item.name,
                  image: item.image,
                  description: item.variations
                    ?.map((variation) => `${variation.name}:${variation.value}`)
                    .join(" | "),
                }}
                onEdit={() => onEditItem(item.id)}
                addCatalogToDeletionList={addElementToDeletionList}
                removeCatalogFromDeletionList={removeElementFromDeletionList}
              />
            ))
          ) : (
            <NoDataFound className="py-5 w-full min-h-48 flex justify-center items-center" />
          )}
          <AlertDialog open={alertModalIsOpen}>
            <ModalDialog
              title={t("dialog.title")}
              details={t("dialog.details")}
              cancel={t("dialog.cancel")}
              confirm={t("dialog.confirm")}
              theme="red"
              isPending={isPending}
              onCancel={cancelDeletion}
              onConfirm={deleteAllElements}
            />
          </AlertDialog>
        </div>
      </div>
    )
  );
}
