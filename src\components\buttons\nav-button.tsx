import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { HTMLAttributes } from "react";

export interface NavButtonPropsType
  extends HTMLAttributes<HTMLAnchorElement & HTMLButtonElement> {
  link?: string;
  writing?: "left" | "right";
  fontFamily?: "tajawal";
  textStyle?: "TS1" | "TS2" | "TS3" | "TS4" | "HS";
  whiteColor?: boolean;
}

export function NavButton({
  link,
  children,
  writing = "left",
  textStyle = "TS4",
  whiteColor = false,
  className,
  ...props
}: NavButtonPropsType) {
  return link ? (
    <a
      {...props}
      href={link}
      id="nav-link-button"
      className={cn(
        "relative w-fit text-nowrap disabled: before:absolute  before:content-[''] before:bottom-[-5px] before:h-[1px] before:w-0 hover:before:w-full focus:before:w-full before:duration-500",
        className,
        {
          "before:bg-white focus:text-white": whiteColor,
          "before:bg-purple hover:text-purple focus:text-purple ": !whiteColor,
          "before:left-0": writing === "left",
          "before:right-0": writing === "right",
        }
      )}
    >
      <Text textStyle={textStyle} className={`align-middle`}>
        {children}
      </Text>
    </a>
  ) : (
    <button
      {...props}
      id="nav-button"
      className={cn(
        "relative w-fit text-nowrap disabled: before:absolute  before:content-[''] before:bottom-[-5px] before:h-[1px] before:w-0 hover:before:w-full focus:before:w-full before:duration-500",
        className,
        {
          "before:bg-white focus:text-white": whiteColor,
          "before:bg-purple hover:text-purple focus:text-purple ": !whiteColor,
          "before:left-0": writing === "left",
          "before:right-0": writing === "right",
        }
      )}
    >
      <Text className={`align-middle`} textStyle={textStyle} color="">
        {children}
      </Text>
    </button>
  );
}
