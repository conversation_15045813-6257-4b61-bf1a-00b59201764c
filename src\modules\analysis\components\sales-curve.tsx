"use client";
import Dashboard<PERSON><PERSON><PERSON>ontainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import useSalesAnalysis from "@/modules/analysis/hooks/use-sales-analysis";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import { Skeleton } from "../../../components/ui/skeleton";
import NoDataFound from "../../../components/no-data-found";
import Text from "@/styles/text-styles";
import TimeFilter from "./filtering-tools/period-filter";

const SimpleAreaChart = dynamic(
  () => import("@/modules/analysis/components/charts/simple-area-chart"),
  {
    ssr: false,
  }
);

export default function SalesCurve() {
  const t = useTranslations("dashboard.totalSales");
  const { sales, isLoading, period, setPeriod, error } = useSalesAnalysis();

  return !(isLoading || sales === undefined) ? (
    sales && error?.status !== 403 && (
      <DashboardListsContainer
        titleWrapper={
          <div className="flex flex-col space-y-1">
            <Text textStyle="TS5" className="">
              {t("title")}
            </Text>
            <Text textStyle="TS4" className="font-bold">
              {`${sales?.totalRevenue}${sales.dailyRevenue[0]?.currency}`}
            </Text>
          </div>
        }
        className="flex-1 flex justify-center items-end"
        headerElement={
          <TimeFilter
            onChange={(period: string) => setPeriod(period)}
            selectedDate={period}
          />
        }
      >
        <div className="flex-1 w-full h-full flex flex-col justify-center space-y-3">
          <div className="pt-5 h-full flex-1 flex items-center">
            <div className="flex items-center justify-center w-full L:h-[350px] h-[300px]">
              {sales ? (
                <SimpleAreaChart data={sales.dailyRevenue} />
              ) : (
                <NoDataFound />
              )}
            </div>
          </div>
        </div>
      </DashboardListsContainer>
    )
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex items-center">
        <Skeleton className="h-96 w-full" />
      </div>
    </DashboardListsContainerSkeleton>
  );
}
