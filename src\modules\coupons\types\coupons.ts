export interface CouponType {
  id: string;
  description?: string;
  maxRedemptions?: number;
  maxUsesPerUser?: number;
  code: string;
  productItemIds: string[];
  appliesToAllProducts: boolean;
  allowOnPromotions?: boolean;
  unlimitedUsage?: boolean;
  freeShipping?: boolean;
  status: string;
  discount: {
    type: "percentage" | "amount";
    value: number;
  };
  period: {
    from?: string;
    to?: string;
    forever: boolean;
  };
  totalVoucherRedemptionsByUsers: number;
}

export interface CouponInResponseType {
  id: string;
  description?: string;
  maxRedemptions?: number;
  maxUsesPerUser?: number;
  code: string;
  productItemIds: string[];
  appliesToAllProducts: boolean;
  status: string;
  allowOnPromotions?: boolean;
  unlimitedUsage?: boolean;
  freeShipping?: boolean;
  discount: {
    type: "percentage" | "amount";
    value: number;
  };
  period: {
    from?: string;
    to?: string;
    forever: boolean;
  };
  totalVoucherRedemptionsByUsers: number;
}

export interface UploadedCouponType {
  description?: string;
  maxRedemptions?: number;
  maxUsesPerUser?: number;
  code: string;
  productItemIds: string[];
  appliesToAllProducts?: boolean;
  allowOnPromotions?: boolean;
  unlimitedUsage?: boolean;
  freeShipping?: boolean;
  discount?: {
    type: "percentage" | "amount";
    value: number;
  };
  period: {
    from?: string;
    to?: string;
    forever: boolean;
  };
  totalVoucherRedemptionsByUsers: number;
}

export interface ErrorDataResponse {
  message: string;
  code: string;
}
