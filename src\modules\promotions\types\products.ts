export interface PromotionProductType {
  id: string;
  categoryIds: string[];
  brand: { id: string; name: string };
  name: string;
  description: string | null;
  displayOrder: number;
  items: PromotionItemType[];
}

export interface PromotionProductInResponseType {
  id: string;
  categoryIds: string[];
  brand: { id: string; name: string };
  name: string;
  description: string | null;
  displayOrder: number;
  items: PromotionItemInResponseType[];
}

export interface PromotionItemType {
  id: string;
  name: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  quantity: number;
  image: string;
  images: string[];
  prices: PriceType[];
  variations: VariationType[];
}

export interface PromotionProductItemType extends PromotionItemType {
  name: string;
}

export interface PromotionItemInResponseType {
  id: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  image: string;
  images: string[];
  quantity: number;
  prices: PriceInResponseType[];
  variation?: VariationType[];
}

export interface PriceType {
  currency: string;
  realPrice: number;
  promotionalPrice: number;
}

export interface PriceInResponseType {
  regularPrice: number;
  promotionalPrice: number;
  currency: string;
}

export interface VariationType {
  name: string;
  value: string;
}
