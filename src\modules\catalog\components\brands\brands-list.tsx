"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import useElementsDeletion from "../../hooks/use-elements-deletion";
import { useRouter } from "next/navigation";
import TrashIcon from "@assets/icons/management/trash-icon";
import Text from "@/styles/text-styles";
import { PlusIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import useBrands from "../../hooks/brands/use-brands";
import PaginationMangement from "@/components/pagination/pagination-management";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import Searchbar from "@/components/searchbar";
import BrandContainer from "./brand-container";
import { useCurrentUrl } from "@/hooks/urls-management/use-current-url";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";

export default function BrandsList() {
  const {
    brands,
    brandsAreLoading,
    page,
    pagesNumber,
    setPage,
    searchedBrands,
    searchBrands,
    records,
  } = useBrands({ limit: 20, paginationAffectUrl: true });
  const t = useTranslations("BrandsManagement");
  const router = useRouter();
  const {
    isPending,
    addElementToDeletionList,
    removeElementFromDeletionList,
    deleteAllElements,
    alertModalIsOpen,
    cancelDeletion,
    onDelete,
  } = useElementsDeletion("brand");
  const currentUrl = useCurrentUrl();

  return (
    <DashboardListsContainer
      title={t("title")}
      headerElement={
        <Searchbar
          searchedContent={searchedBrands}
          setSearchedContent={searchBrands}
        />
      }
    >
      {!(brandsAreLoading || brands === undefined) ? (
        <div className="flex flex-col space-y-5 mt-3">
          {brands && brands.length > 0 && (
            <div className="w-full flex regularL:flex-row flex-col gap-2 justify-end mt-3">
              <button
                onClick={onDelete}
                className="bg-red text-white self-end regularL:order-1 order-2 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
                id="delete-brands-button"
              >
                <TrashIcon />
                <Text textStyle="TS7">{t("delete")}</Text>
              </button>
              <button
                onClick={() =>
                  router.push(
                    `/brands/addition?${getPreviousUrlQueryParam(currentUrl)}`
                  )
                }
                className="bg-[#F2F2F2] text-black self-end regularL:order-2 order-1 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
                id="add-brand-button"
              >
                <PlusIcon />
                <Text textStyle="TS7">{t("add")}</Text>
              </button>
            </div>
          )}
          <div className="flex flex-col space-y-0">
            {brands && brands.length > 0 ? (
              brands.map((brand) => (
                <BrandContainer
                  key={brand.id}
                  type="brand"
                  catalog={{
                    id: brand.id,
                    name: brand.name,
                    image: brand.image,
                  }}
                  onEdit={() =>
                    router.push(
                      `/brands/${brand.slug}/edition?${getPreviousUrlQueryParam(
                        currentUrl
                      )}`
                    )
                  }
                  addCatalogToDeletionList={addElementToDeletionList}
                  removeCatalogFromDeletionList={removeElementFromDeletionList}
                />
              ))
            ) : (
              <NoDataFound className="py-5 w-full min-h-48 flex justify-center items-center" />
            )}
            <AlertDialog open={alertModalIsOpen}>
              <ModalDialog
                title={t("dialog.title")}
                details={t("dialog.details")}
                cancel={t("dialog.cancel")}
                confirm={t("dialog.confirm")}
                theme="red"
                isPending={isPending}
                onCancel={cancelDeletion}
                onConfirm={deleteAllElements}
              />
            </AlertDialog>
          </div>
          {pagesNumber > 1 && (
            <div className="flex justify-center">
              <PaginationMangement
                records={records}
                currentPage={page}
                pagesNumber={pagesNumber}
                changePage={setPage}
              />
            </div>
          )}
        </div>
      ) : (
        <DashboardListsContainerSkeleton>
          <div className="w-full flex flex-col space-y-3">
            <div className="w-full flex justify-end space-x-2">
              <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
              <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
            </div>
            <div className="flex flex-wrap L:justify-start justify-center gap-2">
              {Array.from({ length: 5 }).map((_, idx) => (
                <div className="flex flex-col space-y-3" key={idx}>
                  <Skeleton className="L:h-60 h-48 L:w-60 w-48 rounded-[20px]" />
                  <Skeleton className="h-5 L:w-40 w-32 rounded-[20px]" />
                </div>
              ))}
            </div>
          </div>
        </DashboardListsContainerSkeleton>
      )}
    </DashboardListsContainer>
  );
}
