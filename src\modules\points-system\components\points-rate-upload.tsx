"use client";
import { Info } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Label } from "@/components/ui/label";
import usePointsRate from "../hooks/use-points-rate";
import usePointsRateUpload from "../hooks/use-points-rate-upload";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import FormSubmission from "@/modules/catalog/components/form-submission";
import { useRouter } from "next/navigation";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { Separator } from "@/components/ui/separator";
import { disableScrollOnNumberInput } from "@/utils/number-input";

export default function PointsRateUpload() {
  const t = useTranslations("pointsRateManagement.pointsRatePage");
  const uploadContent = useTranslations("shared.forms.upload");

  const router = useRouter();

  const { pointsRate: defaultPointsRate } = usePointsRate();
  const {
    submitPointsRate,
    isPending,
    warning,
    pointsRate,
    setPointsRate,
    successfullyChanged,
  } = usePointsRateUpload({
    defaultRate: defaultPointsRate ? defaultPointsRate.rate : 0,
  });
  const { currency } = useCurrency();

  return (
    <FormSubmission
      cancel={uploadContent("cancel")}
      submit={uploadContent("create")}
      onCancel={() => router.push("/")}
      onSubmit={submitPointsRate}
      isPending={isPending}
      hideTopButtons
    >
      <div className="rounded-2xl border border-lightGray bg-white p-4 flex flex-col space-y-4">
        <h2>
          <Text textStyle="TS5" className="font-bold text-black">
            {t("title")}
          </Text>
        </h2>
        <Separator />
        <Card className="border-0 p-4 flex flex-col space-y-3">
          <CardHeader className="p-0">
            <CardTitle className="text-xl text-blue">
              <Text textStyle="TS5">{t("configTitle")}</Text>
            </CardTitle>
            <CardDescription>
              <Text textStyle="TS6">
                {t("configDescription", { currency })}
              </Text>
            </CardDescription>
          </CardHeader>
          <Separator />
          <CardContent className="p-0 pt-6">
            {warning !== "" && (
              <p className="w-80">
                <Text textStyle="TS8">{t("rateTooltip")}</Text>
              </p>
            )}

            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label
                    htmlFor="conversionRate"
                    className="text-base font-medium"
                  >
                    {t("rateLabel", {
                      currency,
                    })}
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0 ml-2">
                          <Info className="h-4 w-4 text-blue" />
                          <span className="sr-only">Info</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="bg-blue">
                        <p className="w-80">
                          <Text textStyle="TS8">
                            {t("rateTooltip", { currency })}
                          </Text>
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="conversionRate"
                  type="number"
                  onWheel={disableScrollOnNumberInput}
                  step="0.001"
                  min="0.000"
                  value={pointsRate}
                  onChange={(e) => setPointsRate(Number(e.target.value))}
                />
              </div>

              <div className="w-full flex flex-col space-y-2">
                {[100, 1000, 10000, 100000].map((boughtAmount) => (
                  <div key={boughtAmount} className="w-full font-bold">
                    <p className=" text-gray-500">
                      <Text textStyle="TS6">
                        {`${t("offertDinars", {
                          amount: (boughtAmount * pointsRate).toFixed(2),
                          currency,
                        })} = `}
                      </Text>
                      <Text textStyle="TS6" className="text-blue">
                        {`${t("boughtDinars", {
                          amount: boughtAmount,
                          currency,
                        })}`}
                      </Text>
                    </p>
                  </div>
                ))}
              </div>

              <div className="pt-2">
                <p className=" text-gray-500">
                  <Text textStyle="TS7">
                    {`${t("actualRate")} ${t("offertDinars", {
                      amount: (100 * pointsRate).toFixed(2),
                      currency,
                    })} = `}
                  </Text>
                  <Text textStyle="TS7" className="text-blue">
                    {`${t("boughtDinars", {
                      amount: 100,
                      currency,
                    })}`}
                  </Text>
                </p>
                {defaultPointsRate && (
                  <p className=" text-gray-500">
                    <Text textStyle="TS6">
                      {t.rich("pointsRatePreview", {
                        currency,
                        rate: () => (
                          <span className="text-blue">
                            {defaultPointsRate.rate}
                          </span>
                        ),
                      })}
                    </Text>
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </FormSubmission>
  );
}
