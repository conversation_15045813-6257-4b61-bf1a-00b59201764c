import { getNameChangementGeneralWarning } from "@auth/utils/warnings/general-warning";
import { useRouter } from "next/navigation";
import { FormEvent, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import { changeName } from "../services/name-changement";
import { verifyNameChangementData } from "../validation/name-changement-verification";
import { getPasswordChangementWarning } from "../utils/warnings/server-response-warning";
import { useQueryClient } from "@tanstack/react-query";

export default function useNameChangement() {
  const [isLoading, setIsLoading] = useState(false);
  const [nameChanged, setNameChanged] = useState(false);
  const [newName, setNewName] = useState("");
  const formRef = useRef<HTMLFormElement>(null);
  const router = useRouter();
  const t = useTranslations("auth");
  const queryClient = useQueryClient();

  const [warning, setWarning] = useState({
    newName: "",
    getGeneralWarning: "",
  });

  function submitName(event: FormEvent) {
    event.preventDefault();

    if (!formRef.current) return null;

    const formData = new FormData(formRef.current);
    const name = newName;

    const nameVerification = verifyNameChangementData(name);

    if (!nameVerification.ok) {
      setWarning({
        ...nameVerification.warning,
        getGeneralWarning: getNameChangementGeneralWarning(name, t),
      });
    } else {
      setIsLoading(true);

      // Clearing all warnings
      setWarning({
        newName: "",
        getGeneralWarning: "",
      });

      // Submit verified name change request
      changeName({
        name: name,
      })
        .then((res) => {
          setIsLoading(false);

          if (res?.ok) {
            formRef.current?.reset();
            setNameChanged(true);

            setTimeout(() => {
              setNameChanged(false);
            }, 4000);

            queryClient.invalidateQueries({ queryKey: ["user-data"] });
          } else {
            const warning = getPasswordChangementWarning(
              res.status,
              res.code as string,
              t
            );
            setWarning({
              newName: "",
              getGeneralWarning: warning?.generalWarning as string,
            });
          }
        })
        .catch(() => {
          router.push(`/`);
        });
    }
  }

  return {
    isLoading,
    submitName,
    newName,
    setNewName,
    formRef,
    warning,
    nameChanged,
  };
}
