import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface BrandType {
  id: string;
  name: string;
  image: string;
  description: string;
  displayOrder: number;
  metaContent: SeoMetaContentType | null;
  slug: string;
}
export interface BrandInResponseType {
  id: string;
  name: string;
  image: string;
  description: string;
  displayOrder: number;
  metaContent: SeoMetaContentTypeInResponse;
  slug: string;
}
