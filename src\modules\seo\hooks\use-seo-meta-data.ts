import { useEffect, useRef, useState } from "react";
import {
  type Language,
  type MultilanguageSeoType,
  type MultilanguageSeoContent,
} from "../types/multilanguage-seo";

const useSeoMetaData = (initialMetaContent?: MultilanguageSeoType | null) => {
  const [metaContent, setMetaContent] = useState<MultilanguageSeoType>({
    arabic: { metaTitle: "", metaDescription: "", tags: [] },
    french: { metaTitle: "", metaDescription: "", tags: [] },
    english: { metaTitle: "", metaDescription: "", tags: [] },
  });
  const [seoActiveLanguage, setSeoActiveLanguage] =
    useState<Language>("french");

  const isInitialized = useRef(false);

  useEffect(() => {
    if (initialMetaContent && !isInitialized.current) {
      setMetaContent(initialMetaContent);
      isInitialized.current = true;
    }
  }, [initialMetaContent]);

  const handleMetaTitleChange = (newMetaTitle: string) => {
    setMetaContent((prev) => ({
      ...prev,
      [seoActiveLanguage]: {
        ...prev[seoActiveLanguage],
        metaTitle: newMetaTitle,
      },
    }));
  };

  const handleMetaDescriptionChange = (newMetaDescription: string) => {
    setMetaContent((prev) => ({
      ...prev,
      [seoActiveLanguage]: {
        ...prev[seoActiveLanguage],
        metaDescription: newMetaDescription,
      },
    }));
  };

  const addNewKeyword = (newKeyword: string) => {
    setMetaContent((prev) => ({
      ...prev,
      [seoActiveLanguage]: {
        ...prev[seoActiveLanguage],
        tags: [newKeyword, ...prev[seoActiveLanguage].tags],
      },
    }));
  };

  const removeKeyword = (removedKeyword: string) => {
    setMetaContent((prev) => ({
      ...prev,
      [seoActiveLanguage]: {
        ...prev[seoActiveLanguage],
        tags: prev[seoActiveLanguage].tags.filter(
          (keyword) => keyword !== removedKeyword
        ),
      },
    }));
  };

  const handleSeoLanguageChange = (language: string) => {
    if (
      language === "arabic" ||
      language === "french" ||
      language === "english"
    ) {
      setSeoActiveLanguage(language);
    }
  };

  const getMetaContent = (language: string) => {
    const contentMap = {
      arabic: {
        title: metaContent.arabic.metaTitle,
        description: metaContent.arabic.metaDescription,
        language: "arabic",
      },
      french: {
        title: metaContent.french.metaTitle,
        description: metaContent.french.metaDescription,
        language: "french",
      },
      english: {
        title: metaContent.english.metaTitle,
        description: metaContent.english.metaDescription,
        language: "english",
      },
    };

    return {
      title: contentMap[language as Language].title,
      description: contentMap[language as Language].description,
      keywords: metaContent[language as Language].tags,
    };
  };

  const getMultilanguageMetaContent = () => {
    const content: MultilanguageSeoContent[] = [];

    ["arabic", "french", "english"].forEach((lang) => {
      const langKey = lang as Language;
      const title = metaContent[langKey].metaTitle.trim();
      const description = metaContent[langKey].metaDescription.trim();
      const tags = metaContent[langKey].tags;

      const languageContent: MultilanguageSeoContent = {
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      };

      if (title) {
        languageContent.title = title;
      }

      if (description) {
        languageContent.description = description;
      }

      if (tags && tags.length > 0) {
        languageContent.tags = tags;
      }

      content.push(languageContent);
    });

    return { content };
  };

  return {
    metaTitle: metaContent[seoActiveLanguage].metaTitle,
    metaDescription: metaContent[seoActiveLanguage].metaDescription,
    keywords: metaContent[seoActiveLanguage].tags,
    metaContent,
    seoActiveLanguage,
    handleSeoLanguageChange,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMetaContent,
    getMultilanguageMetaContent,
  };
};

export default useSeoMetaData;
