import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface MultilanguageBlogContent {
  name: string;
  description: string;
  details: string;
  language: string;
}

export interface MultilanguageBlogType {
  id: string;
  content: MultilanguageBlogContent[];
  displayOrder: number;
  metaContent: SeoMetaContentType | null;
  slug: string;
  image?: string;
}

export interface MultilanguageBlogInResponseType {
  id: string;
  content: MultilanguageBlogContent[];
  displayOrder: number;
  metaContent: SeoMetaContentTypeInResponse;
  slug: string;
  image?: string;
}

export type LanguageKey = "arabic" | "french" | "english";
