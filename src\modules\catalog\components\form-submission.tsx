import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import React, { FormEvent } from "react";

interface Props {
  children: React.ReactNode[] | React.ReactNode;
  onSubmit: (event: FormEvent) => void;
  onCancel: () => void;
  submit: string;
  cancel: string;
  hideTopButtons?: boolean;
  isPending?: boolean;
}

export default function FormSubmission(props: Props) {
  return (
    <div className="flex flex-col items-end space-y-5 ">
      {props.hideTopButtons ? null : (
        <div className="flex space-x-3">
          <div
            onClick={props.isPending ? () => {} : props.onCancel}
            className={cn(
              "px-6 py-2 border rounded-sm text-blue active:scale-95 opacity-85 duration-300",
              {
                "cursor-pointer": !props.isPending,
              }
            )}
            id="form-cancel-button-top"
          >
            <Text textStyle="TS5">{props.cancel}</Text>
          </div>
          <button
            onClick={props.onSubmit}
            disabled={props.isPending}
            className={cn(
              "px-6 py-2 rounded-sm text-white bg-blue active:scale-95 opacity-85 duration-300",
              {
                "opacity-70 scale-95": props.isPending,
              }
            )}
            id="form-submit-button-top"
          >
            <Text textStyle="TS5">{props.submit}</Text>
          </button>
        </div>
      )}
      <div className="w-full">{props.children}</div>
      <div className="w-full h-[1px] bg-lightGray" />
      <div className="flex space-x-3">
        <div
          onClick={props.isPending ? () => {} : props.onCancel}
          className={cn(
            "px-6 py-2 border rounded-sm text-blue active:scale-95 opacity-85 duration-300",
            {
              "cursor-pointer": !props.isPending,
            }
          )}
          id="form-cancel-button-bottom"
        >
          <Text textStyle="TS5">{props.cancel}</Text>
        </div>
        <button
          onClick={props.onSubmit}
          disabled={props.isPending}
          className={cn(
            "px-6 py-2 rounded-sm text-white bg-blue active:scale-95 opacity-85 duration-300",
            {
              "opacity-70 scale-95": props.isPending,
            }
          )}
          id="form-submit-button-bottom"
        >
          <Text textStyle="TS5">{props.submit}</Text>
        </button>
      </div>
    </div>
  );
}
