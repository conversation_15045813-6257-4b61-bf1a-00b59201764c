import { getEmailSchema } from "@auth/validation/schemas/auth/email";
import { useState } from "react";
import useCodeVerification from "./use-code-verification";
import { verifyPasswords } from "@auth/validation/dom-extraction-verification/password-verification";
import { sendResetPasswordEmail } from "@/modules/auth/services/reset-password/email-submition";
import { resetPassword } from "@/modules/auth/services/reset-password/password-submition";
import { getServerErrorWarning } from "../utils/warnings/general-warning";
import { useTranslations } from "next-intl";
import { useAuthMode } from "../store/auth-mode-store";

export default function useResetPassword() {
  const [step, setStep] = useState<"email" | "code" | "password">("email");
  const { setMode } = useAuthMode();
  const [email, setEmail] = useState("");
  const [startPasswordStep, setStartPasswordStep] = useState(false); //used to start display the password warning
  const t = useTranslations("auth");
  const [warning, setWarning] = useState({
    email: "",
    generalWarning: "",
  });
  const [passwordWarning, setPasswordWarning] = useState({
    password: "",
    confirmationPassword: "",
    generalWarning: "",
  });
  const { verifyCode, displayedTimer, startTimer, code, setCode } =
    useCodeVerification();
  const [isLoading, setIsLoading] = useState(false);

  function submitEmail() {
    //email verification
    const emailSchema = getEmailSchema();
    const verificationResult = emailSchema.safeParse({ email });

    if (!verificationResult.success)
      setWarning({
        email: verificationResult.error.errors[0].message,
        generalWarning: verificationResult.error.errors[0].message,
      });
    else {
      setIsLoading(true);
      //submit email on the server side
      sendResetPasswordEmail(email).then((res) => {
        if (!res.ok) {
          setWarning({
            email: "",
            generalWarning: getServerErrorWarning(res.status, t),
          });
        } else {
          setStep("code");
          startTimer();

          if (warning.email !== "" || warning.generalWarning !== "")
            setWarning({
              email: "",
              generalWarning: "",
            });
        }

        setIsLoading(false);
      });
    }
  }

  function submitCode() {
    setIsLoading(true);
    verifyCode(email, code).then((res) => {
      if (res.verified) {
        setStep("password");
        setTimeout(() => {
          setStartPasswordStep(true);
        }, 500);
      } else
        setWarning({
          email: "",
          generalWarning: res.message,
        });

      setIsLoading(false);
    });
  }

  function submitPassword() {
    const passwordVerification = verifyPasswords();

    if (!passwordVerification.ok)
      setPasswordWarning(passwordVerification.warning);
    else {
      setIsLoading(true);

      //submit verified password on server side
      resetPassword({
        email,
        code,
        password: passwordVerification.password,
      }).then((res) => {
        setIsLoading(false);

        if (res.ok) setMode("signIn");
        else
          setPasswordWarning({
            password: "",
            confirmationPassword: "",
            generalWarning: getServerErrorWarning(res.status, t),
          });
      });
    }
  }

  return {
    step,
    warning,
    passwordWarning,
    submitEmail,
    displayedTimer,
    submitCode,
    code,
    setCode,
    email,
    setEmail,
    submitPassword,
    startPasswordStep,
    isLoading,
    setStep,
  };
}
