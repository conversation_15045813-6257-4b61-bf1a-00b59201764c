import { HTMLAttributes, useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import Image from "next/image";
import EditionIcon from "@assets/icons/management/edition-icon";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckedState } from "@radix-ui/react-checkbox";
import TrashIcon from "@assets/icons/management/trash-icon";
import { PromotionProductItemType } from "../../types/products";

interface Props extends HTMLAttributes<"div"> {
  productItem: PromotionProductItemType;
  onEdit: () => void;
  onDelete: () => void;
  onCheck: (
    checked: CheckedState,
    productItem: PromotionProductItemType
  ) => void;
  onDeleteCheck: (checked: CheckedState, productItemId: string) => void;
  checked: CheckedState;
  availablePromotion: boolean;
  choosedFilter: string;
}

export default function PromotionProductContainer({
  productItem,
  choosedFilter,
  ...props
}: Props) {
  const [productImage, setProductImage] = useState("/not-found/image.png");

  useEffect(() => {
    if (productItem.image) setProductImage(productItem.image);
  }, [productItem]);

  return (
    <div
      className={cn(
        "border rounded-md px-[14px] py-3 flex items-center justify-between space-x-2",
        props.className,
        {
          "bg-blue bg-opacity-20": props.checked,
        }
      )}
    >
      <label className="min-h-[80px] flex-1 flex items-center space-x-3 cursor-pointer">
        <Checkbox
          className="text-blue text-opacity-50 data-[state=checked]:bg-white outline-none bg-white w-5 h-5"
          checked={props.checked}
          onCheckedChange={(checked) => {
            if (choosedFilter === "promoted")
              props.onDeleteCheck(checked, productItem.id);
            else props.onCheck(checked, productItem);
          }}
        />

        {productItem.image ? (
          <Image
            alt="ProductItem image"
            src={productImage}
            width={30}
            height={30}
            className="group-hover:opacity-80"
            onError={() => setProductImage("/not-found/image.png")}
          />
        ) : null}

        <div className="flex flex-col w-full max-w-full">
          <div className="overflow-hidden">
            <Text
              textStyle="TS5"
              className="text-black overflow-hidden overflow-ellipsis block max-w-1/2"
            >
              {productItem.name}
            </Text>
          </div>
          {props.availablePromotion && (
            <div className="flex space-x-1 overflow-hidden">
              <Text
                textStyle="TS6"
                className="text-blue text-nowrap overflow-hidden overflow-ellipsis block max-w-full"
                style={{ flexShrink: 1 }}
              >
                {`${productItem.prices[0].promotionalPrice} ${productItem.prices[0].currency}`}
              </Text>
              <Text
                textStyle="TS7"
                className="text-blue/70 text-nowrap overflow-hidden overflow-ellipsis block max-w-full line-through"
                style={{ flexShrink: 1 }}
              >
                {`${productItem.prices[0].realPrice} ${productItem.prices[0].currency}`}
              </Text>
            </div>
          )}
        </div>
        {/* <div className="flex-1 flex flex-col items-center">
        {productItem.promotion.map((promotion) =>
          promotion.discount != 0 ? (
            <Text key={promotion.id} textStyle="TS6" className="text-black">
              {`${promotion.discount * 100}% on ${promotion.currency}`}
            </Text>
          ) : null
        )}
      </div> */}
      </label>

      <div className="flex L:space-x-10 space-x-5">
        <div
          onClick={props.availablePromotion ? props.onDelete : props.onEdit}
          className="cursor-pointer flex rounded-s  space-x-2 active:scale-95 active:opacity-85 duration-300"
        >
          {props.availablePromotion ? (
            <TrashIcon color="#151B26" />
          ) : (
            <EditionIcon color="#151B26" />
          )}
        </div>
      </div>
    </div>
  );
}
