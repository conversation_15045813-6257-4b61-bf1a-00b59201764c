import useUser from "@/modules/auth/hooks/use-user";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { ProductType } from "@/modules/catalog/types/products";
import { retrieveEditableProduct } from "@/modules/catalog/services/products/editable-product-extraction";

interface UseEditableProductParams {
  productSlug: string;
  language?: string;
}

export default function useEditableProduct(
  params: UseEditableProductParams | string
) {
  const { user } = useUser();
  const pathname = usePathname();

  const productSlug = typeof params === "string" ? params : params.productSlug;
  const language = typeof params === "string" ? undefined : params.language;

  const { data, isLoading, refetch } = useQuery<ProductType>({
    queryKey: ["product", productSlug, language, user, pathname],
    queryFn: () => retrieveEditableProduct(productSlug, language),
    placeholderData: keepPreviousData,
    enabled: user !== null,
  });
  return {
    isLoading,
    product: data as ProductType,
    refetch,
  };
}
