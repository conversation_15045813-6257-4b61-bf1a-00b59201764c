export interface CustomerType {
  id: string;
  name: string;
  email: string;
  role: string;
  points: number;
  // ordersNumber: number;
  // totalRevenue: number;
}

export interface CustomerInResponseType {
  id: string;
  name: string;
  email: string;
  role: string;
  points: number;
  // ordersNumber: number;
  // totalRevenue: number;
}

export interface ErrorDataResponse {
  message: string;
  code: string;
}
