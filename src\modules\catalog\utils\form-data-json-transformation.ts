export function formDataJsonTransformation(elementData: FormData) {
  const jsonObject: Record<string, any> = {};

  elementData.forEach((value, key) => {
    if (key in jsonObject) {
      if (Array.isArray(jsonObject[key])) {
        jsonObject[key].push(value);
      } else {
        jsonObject[key] = [jsonObject[key], value];
      }
    } else {
      if (value === "null") jsonObject[key] = null;
      else jsonObject[key] = value;
    }
  });

  return jsonObject;
}
