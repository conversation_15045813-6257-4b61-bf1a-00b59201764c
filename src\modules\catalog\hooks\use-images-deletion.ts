import { useEffect, useState } from "react";
import { CatalogElementType } from "../types";
import useUser from "@/modules/auth/hooks/use-user";
import { useQueryClient } from "@tanstack/react-query";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { deleteImageOnServerSide } from "../services/image-deletion";

export default function useImagesDeletion(
  type: CatalogElementType,
  elementId: string,
  defaultImages?: string[]
) {
  const [images, setImages] = useState<string[]>([]);
  const [deletedImages, setDeletedImages] = useState<string[]>([]);
  const [isPending, setIsPending] = useState(false);
  const queryClient = useQueryClient();
  const [warning, setWarning] = useState("");
  const { user } = useUser();
  const t = useTranslations("warnings");

  useEffect(() => {
    if (defaultImages) setImages(defaultImages);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultImages]);

  useEffect(() => {
    if (defaultImages) {
      const imagesToDelete = defaultImages.filter(
        (image) => !images.includes(`${image}`)
      );

      setDeletedImages(imagesToDelete);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [images]);
  const deleteImages = async () => {
    setIsPending(true);

    try {
      await Promise.all(
        deletedImages.map((image) => deleteImageOnServerSide(elementId, image))
      );
      if (warning !== "") setWarning("");

      setImages([]);
      queryClient.invalidateQueries({
        queryKey: [
          type === "product"
            ? "products"
            : type === "category"
            ? "categories"
            : "items",
          user,
        ],
      });
    } catch (throwedError) {
      const error = throwedError as CustomError;
      if (error.status === 500) setWarning(t("serverError"));

      throw new CustomError("Server Erro!", 500);
    } finally {
      setIsPending(false);
    }
  };
  return {
    isPending,
    warning,
    deleteImages,
    images,
    setImages,
  };
}
