import { GET } from "@/lib/http-methods";
import { AxiosError } from "axios";
import { refreshToken } from "@auth/services/refresh-token";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import {
  ShippingDetails,
  ShippingDetailsInRespone,
} from "../types/shipping-cost";
import castShippingDetails from "../utils/data-management/types-casting/shipping-details";
import { CustomError } from "@/utils/custom-error";

export async function retreiveShippingsDetails(): Promise<
  ShippingDetails[] | null
> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };
  try {
    const res = await GET(`/shippings/rates`, header);

    return (res.data as ShippingDetailsInRespone[]).map((shippingDetails) =>
      castShippingDetails(shippingDetails)
    );
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retreiveShippingsDetails());

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);
      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
