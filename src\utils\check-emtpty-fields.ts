export default function checkEmptyFields(ids: string[]) {
  let wrongInput = false;

  ids.forEach((id) => {
    const input = document.getElementById(id) as HTMLInputElement;

    if (input.value.trim() === "") {
      wrongInput = true;

      input.classList.add("animate-error-shake-with-color");

      setTimeout(() => {
        input.classList.remove("animate-error-shake-with-color");
      }, 500);
    }
  });

  return !wrongInput;
}
