import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import retreiveProductsPromotions from "../../services/products/products-promotions-extraction";
import { ProductType } from "../../types/products";

export default function useProductsPromotions(limit?: number) {
  const { user } = useUser();
  const [currentPage, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { data, isLoading, isError } = useQuery<{
    data: ProductType[];
    pagination: PaginationType;
  }>({
    queryKey: ["products-promotions", user, currentPage],
    queryFn: () => retreiveProductsPromotions(currentPage, limit),
    enabled: user !== null,
  });

  useEffect(() => {
    if (data && data.pagination) {
      setTotalPages(data.pagination.totalPages);
    }
  }, [data]);

  return {
    products: data?.data,
    productsAreLoading: isLoading,
    productsError: isError,
    changePage: setPage,
    currentPage,
    totalPages,
  };
}
