import { PUT } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

interface Params {
  rate: number;
}

export default async function uploadPointsRateToServerSide(params: Params) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await PUT(`/points-rate`, header, { rate: params.rate });

    return res.status;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadPointsRateToServerSide(params)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }
    throw new CustomError("Server Error!", 500);
  }
}
