import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import useUser from "@/modules/auth/hooks/use-user";
import { CustomError } from "@/utils/custom-error";
import { deleteBlogOnServerSide } from "../services/blog-deletion";
import { useTranslations } from "next-intl";

export default function useBlogsDeletion() {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const [blogsIds, setBlogsIds] = useState<string[]>([]);
  const { user } = useUser();
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);
  const deleteAllBlogs = async () => {
    setIsPending(true);

    try {
      await Promise.all(
        blogsIds.map((blogId) => deleteBlogOnServerSide({ blogId }))
      );

      if (warning !== "") setWarning("");

      queryClient.invalidateQueries({
        queryKey: ["blogs"],
        exact: false,
      });

      setAlertModalIsOpen(false);
      setBlogsIds([]);
    } catch (throwedError) {
      const error = throwedError as CustomError;
      if (error.status === 500) setWarning(t("serverError"));
    }

    setIsPending(false);
  };

  const onDelete = () => {
    if (blogsIds.length > 0) setAlertModalIsOpen(true);
  };

  const addBlogToDeletionList = (blogId: string) => {
    setBlogsIds([...blogsIds, blogId]);
  };

  const removeBlogFromDeletionList = (blogId: string) => {
    const filterList = blogsIds.filter((id) => blogId !== id);
    setBlogsIds(filterList);
  };

  const requireDirectDeletion = (blogId: string) => {
    setBlogsIds([blogId]);
    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
  };

  return {
    isPending,
    alertModalIsOpen,
    cancelDeletion,
    warning,
    deleteAllBlogs,
    setBlogsIds,
    addBlogToDeletionList,
    removeBlogFromDeletionList,
    onDelete,
    blogsIds,
    requireDirectDeletion,
  };
}
