import useUser from "@/modules/auth/hooks/use-user";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { CouponType } from "../types/coupons";
import { retrieveEditableCoupon } from "../services/editable-coupon-extraction";

export default function useEditableCoupon(couponId: string) {
  const { user } = useUser();
  const pathname = usePathname();

  const {
    data: coupon,
    isLoading: couponIsLoading,
    error: couponError,
  } = useQuery<CouponType>({
    queryKey: ["coupon", couponId, user, pathname],
    queryFn: () => retrieveEditableCoupon(couponId),
    placeholderData: keepPreviousData,
    enabled: user !== null,
  });

  return {
    couponIsLoading,
    coupon,
    couponError,
  };
}
