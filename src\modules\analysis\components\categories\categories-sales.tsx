"use client";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import useCategoriesSales from "../../hooks/use-categories-sales";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";

const COLORS = ["#1675F2", "#8B41F2", "#F2A516", "#FF8042"];
const PieChart = dynamic(
  () => import("@/modules/analysis/components/charts/pie-chart"),
  {
    ssr: false,
  }
);

export default function CategoriesSales() {
  const t = useTranslations("dashboard.popularCategories");
  const { categories, categoriesAreLoading } = useCategoriesSales();

  return !(categoriesAreLoading || categories === undefined) ? (
    <DashboardListsContainer title={t("title")}>
      <div className="flex justify-center">
        {categories && categories.length > 0 ? (
          <PieChart data={categories.slice(0, 4)} COLORS={COLORS} />
        ) : (
          <NoDataFound className="min-h-32 flex items-center" />
        )}
      </div>
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex items-center">
        <Skeleton className="h-60 w-full" />
      </div>
      <div className="flex flex-col space-y-2">
        {Array.from({ length: 2 }).map((_, idx) => (
          <Skeleton key={idx} className="h-7 w-52" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
