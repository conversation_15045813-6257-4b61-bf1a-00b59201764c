import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import useProducts from "../../hooks/products/use-products";
import { useRouter } from "next/navigation";
import ProductContainer from "./product-container";
import useElementDeletion from "../../hooks/use-element-deletion";
import PaginationMangement from "@/components/pagination/pagination-management";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import Searchbar from "@/components/searchbar";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";
import { useCurrentUrl } from "@/hooks/urls-management/use-current-url";
import ListPicker from "../list-picker";

export default function ProductsList() {
  const t = useTranslations("ProductsManagement");
  const router = useRouter();
  const currentUrl = useCurrentUrl();
  const searchTypes = ["name", "barcode"];
  const criteriaList = [
    "displayOrder",
    "priceAsc",
    "priceDesc",
    "createdAtDesc",
    "createdAtAsc",
    "nameAsc",
    "nameDesc",
    "mostSold",
  ];

  const {
    products,
    productsAreLoading,
    setPage,
    pagesNumber,
    page,
    searchedProducts,
    setCriteria,
    criteria,
    searchProducts,
    setSearchBy,
    searchBy,
    records,
  } = useProducts(20);

  const {
    isPending,
    deleteElement,
    onDelete,
    alertModalIsOpen,
    cancelDeletion,
    warning,
  } = useElementDeletion("product");

  const onEdit = (productSlug: string) => {
    router.push(
      `/products/${productSlug}/edition?${getPreviousUrlQueryParam(currentUrl)}`
    );
  };

  return !productsAreLoading ? (
    <DashboardListsContainer
      title={t("title")}
      includesSearchbar
      headerElement={
        <div className="flex L:flex-row flex-col gap-2 L:items-center items-end">
          <div className="flex flex-wrap gap-2">
            <ListPicker
              label={t("criteria.label")}
              list={t
                .raw("criteria.options")
                .map((type: string, idx: number) => ({
                  name: type,
                  value: criteriaList[idx],
                }))}
              selectedElement={criteria}
              setPickedElement={setCriteria as (value: string) => void}
            />
            <ListPicker
              label={t("searchType.label")}
              list={t
                .raw("searchType.options")
                .map((type: string, idx: number) => ({
                  name: type,
                  value: searchTypes[idx],
                }))}
              selectedElement={searchBy}
              setPickedElement={setSearchBy}
            />
          </div>
          <Searchbar
            searchedContent={searchedProducts}
            setSearchedContent={searchProducts}
          />
        </div>
      }
    >
      <div className="flex flex-col space-y-3">
        {products && products.length > 0 ? (
          products.map((product) => (
            <ProductContainer
              key={product.id}
              product={product}
              onEdit={() => onEdit(product.slug)}
              onDelete={onDelete}
            />
          ))
        ) : (
          <NoDataFound className="py-5 flex justify-center items-center" />
        )}
        {pagesNumber > 1 && (
          <div className="flex justify-center pt-3 px-3">
            <PaginationMangement
              records={records}
              currentPage={page}
              pagesNumber={pagesNumber}
              changePage={setPage}
            />
          </div>
        )}
        <AlertDialog open={alertModalIsOpen}>
          <ModalDialog
            title={t("dialog.title")}
            details={t("dialog.details")}
            cancel={t("dialog.cancel")}
            confirm={t("dialog.confirm")}
            isPending={isPending}
            onCancel={cancelDeletion}
            onConfirm={deleteElement}
            warning={warning}
            theme="red"
          />
        </AlertDialog>
      </div>
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-2">
        {Array.from({ length: 10 }).map((_, idx) => (
          <Skeleton key={idx} className="h-14 w-full" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
