import { useEffect, useState } from "react";
import uploadPromotionOnServerSide from "../services/promotion-upload";
import { useTranslations } from "next-intl";
import { CustomError } from "@/utils/custom-error";
import { toast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { PromotionType, UploadedPromotionType } from "../types/promotions";

export default function usePromotionUpload(
  onClose: () => void,
  editablePromotion?: PromotionType
) {
  const queryClient = useQueryClient();
  const [promotionData, setPromotionData] = useState<UploadedPromotionType>({
    name: "",
    description: "",
    from: "",
    to: "",
    forever: false,
  });
  const [warning, setWarning] = useState("");
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);

  useEffect(() => {
    if (editablePromotion)
      setPromotionData({
        name: editablePromotion.name,
        description: editablePromotion.description,
        forever: editablePromotion.forever,
        from: editablePromotion.startTime,
        to: editablePromotion.endTime,
      });
    else
      setPromotionData({
        name: "",
        description: "",
        from: "",
        to: "",
        forever: false,
      });
  }, [editablePromotion]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (["from", "to"].includes(name))
      setPromotionData((prev) => ({
        ...prev,
        [name]: new Date(value).toISOString(),
      }));
    else setPromotionData((prev) => ({ ...prev, [name]: value }));
  };

  //used bc promotion name is a dropdown menu
  const handleNameChange = (name: string) => {
    setPromotionData((prev) => ({
      ...prev,
      name,
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setPromotionData((prev) => ({ ...prev, forever: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    if (warning !== "") setWarning("");

    e.preventDefault();
    setIsPending(true);
    try {
      const uploadedData = promotionData.forever
        ? {
            name: promotionData.name,
            description: promotionData.description,
            forever: promotionData.forever,
          }
        : promotionData;
      if (
        new Date(promotionData.from as string) >
        new Date(promotionData.to as string)
      )
        throw new CustomError("Invalid Date Range!", 400, "PDate");

      await uploadPromotionOnServerSide(uploadedData);
      onClose();
      // Reset form after submission
      setPromotionData({
        name: "",
        description: "",
        from: "",
        to: "",
        forever: false,
      });
      queryClient.invalidateQueries({
        queryKey: ["promotions"],
        exact: false,
      });
    } catch (error) {
      const customError = error as CustomError;

      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.code === "P4501") {
          setWarning(
            t("upload.promotions.promotionAlreadyExistInThisPeriodWithSameName")
          );
        } else if (customError.code === "PDate")
          setWarning(t("upload.promotions.invalidDateRange"));
        else if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  };

  return {
    handleNameChange,
    handleSubmit,
    handleInputChange,
    handleSwitchChange,
    promotionData,
    isPending,
    warning,
  };
}
