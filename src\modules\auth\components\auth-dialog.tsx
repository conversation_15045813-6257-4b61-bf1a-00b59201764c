"use client";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import Image from "next/image";

import { ScrollArea } from "@/components/ui/scroll-area";
import { LogIn } from "lucide-react";
import ResetPassword from "./reset-password";
import AuthSwitch from "./auth-switch";
import { useAuthMode } from "../store/auth-mode-store";
import { useState } from "react";
import { useAuthDialogState } from "../store/auth-dialog-state-store";
import useUserStore from "../store/user-store";

export const AuthDialog = () => {
  const { mode } = useAuthMode();
  const [email, setEmail] = useState("");
  const { isOpen, setIsOpen } = useAuthDialogState((store) => store);
  const { user, isLoading } = useUserStore((store) => store);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {(!user || isLoading || (user && !user.isAuthenticated)) && (
        <DialogTrigger className="pr-1">
          <LogIn className="text-green" />
        </DialogTrigger>
      )}
      <DialogContent className="L:w-[70%] w-full max-w-[900px]">
        <ScrollArea className="w-full h-[80vh] pr-3">
          <div className="w-full flex flex-col gap-[30px] items-center rounded-2xl XL:flex-row">
            <div className="hidden S:block flex-1 mx-auto">
              <Image
                src={"/backgrounds/auth.png"}
                alt="Sign-up picture"
                width={435}
                height={710}
                className="h-[80vh] aspect-auto"
                priority
              />
            </div>
            <div className="flex-1 w-full tinyL:w-[400px]">
              <DialogHeader>
                <DialogTitle></DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              {mode === "reset" ? (
                <ResetPassword />
              ) : (
                <AuthSwitch setEmail={setEmail} />
              )}
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
