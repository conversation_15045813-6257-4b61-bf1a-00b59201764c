import { PATCH } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import { ErrorDataResponse } from "@/modules/catalog/types/products";

export default async function uploadSeoMetaDataToServerSide(
  jsonObject: Record<string, any>
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/page-palette/landing-page";

    const res = await PATCH(
      `${process.env.BACKEND_ADDRESS}${endpoint}`,
      header,
      jsonObject
    );

    return res.data;
  } catch (error) {
    const axiosError = error as AxiosError<ErrorDataResponse>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadSeoMetaDataToServerSide(jsonObject)
      );

      if (!res) throw new CustomError("Unauthorized", 401);
      return res;
    } else if (axiosError.response?.status === 400) {
      if (axiosError.response?.data.code === "P1000") {
        throw new CustomError("Format de données invalide !", 400);
      }
    }
    throw new CustomError("Server Error!", 500);
  }
}
