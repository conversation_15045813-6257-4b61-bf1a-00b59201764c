import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { castToBrandType } from "../../utils/data-management/types-casting/brands";
import { BrandInResponseType } from "../../types/brands";

export async function retrieveEditableBrand(
  brandSlug: string,
  language?: string
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const url = language
      ? `/brands/${brandSlug}/dashboard?language=${language}`
      : `/brands/${brandSlug}/dashboard`;
    const res = await GET(url, headers);
    return castToBrandType(res.data as BrandInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveEditableBrand(brandSlug, language)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
