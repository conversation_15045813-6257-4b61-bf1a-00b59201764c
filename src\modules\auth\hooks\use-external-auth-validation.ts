import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function useExternalAuth() {
  const t = useTranslations("auth.warnings.externalAuth");

  const router = useRouter();
  const searchParams = useSearchParams();
  const externalAuthResult = searchParams.get("auth");
  const externalAuthStatus = searchParams.get("status");

  const { toast } = useToast();

  useEffect(() => {
    setTimeout(() => {
      if (externalAuthResult === "failed") {
        toast({
          title: t("title"),
          description:
            externalAuthStatus == "409"
              ? t("emailLinkedWithAnAccount")
              : t("generalError"),
        });
        router.replace("/");
      }
    }, 0);
  }, []);
}
