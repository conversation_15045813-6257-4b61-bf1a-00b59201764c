import { useEffect, useState } from "react";
import { FiltersEnum } from "../../types";
import retrievePromotionProducts from "../../services/products/promotion-products-extraction";
import { useQuery } from "@tanstack/react-query";
import { PromotionProductType } from "../../types/products";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import useUser from "@/modules/auth/hooks/use-user";

interface PromotionProductsProps {
  limit: number;
  slug: string;
}

export default function usePromotionProducts({
  limit,
  slug,
}: PromotionProductsProps) {
  const productsFilter: FiltersEnum[] = ["promoted", "unpromoted"];
  const [displayedProductsType, setDisplayedProductsType] = useState<string>(
    productsFilter[1]
  );
  const [displayedFilter, setDisplayedFilter] =
    useState<string>("Sans réduction");
  const [selectedCategories, setSelectedCategories] = useState<
    { id: string; slug: string }[]
  >([]);
  const { user } = useUser();
  const {
    page: currentPage,
    setPage,
    pagesNumber: totalPages,
    setPagesNumber: setTotalPages,
    records,
    setRecords,
  } = usePagination();
  const [searchedProducts, setSearchedProducts] = useState("");
  const { data, isLoading, isError } = useQuery<{
    data: PromotionProductType[];
    pagination: PaginationType;
  }>({
    queryKey: [
      "promotion-products",
      slug,
      user,
      currentPage,
      searchedProducts,
      displayedProductsType,
      selectedCategories,
    ],
    queryFn: () =>
      retrievePromotionProducts({
        limit,
        page: currentPage,
        searchedProducts,
        categoriesSlugs: selectedCategories.map((category) => category.slug),
        inPromotion: displayedProductsType === "promoted",
        slug,
      }),
    enabled: user !== null,
    placeholderData: (prev) => prev,
  });

  useEffect(() => {
    if (data && data.pagination) {
      setTotalPages(data.pagination.totalPages);

      if (data.pagination.totalPages < currentPage) setPage(1);

      if (data?.data) {
        setRecords(data.data.length);
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return {
    records,
    products: data?.data,
    productsAreLoading: isLoading,
    currentPage,
    changePage: setPage,
    setSearchedProducts,
    searchedProducts,
    displayedFilter,
    setDisplayedFilter,
    totalPages,
    productsFilter,
    displayedProductsType,
    setDisplayedProductsType,
    selectedCategories,
    setSelectedCategories,
  };
}
