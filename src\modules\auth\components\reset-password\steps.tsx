"use client";
import { cn } from "@/lib/utils";
import EmailStep from "./email-step";
import CodeStep from "./code-step";
import PasswordStep from "./password-step";
import { useResetPasswordContext } from "../../context/reset-password";

export default function ResetPasswordSteps() {
  const { step, startPasswordStep } = useResetPasswordContext();

  return (
    <div className={"w-full h-full flex overflow-hidden duration-500"}>
      <div
        className={cn("min-w-full duration-500", {
          "translate-x-[-110%]": step !== "email",
          hidden: startPasswordStep,
        })}
      >
        <EmailStep />
      </div>
      <div
        className={cn("min-w-full duration-500", {
          "translate-x-[-200%]": step === "password",
          "translate-x-[-100%]": step === "code",
          hidden: startPasswordStep,
        })}
      >
        <CodeStep />
      </div>
      <div
        className={cn("min-w-full w-full", {
          "translate-x-[-200%]": step === "password" && !startPasswordStep,
          "duration-500": !startPasswordStep,
        })}
      >
        <PasswordStep />
      </div>
    </div>
  );
}
