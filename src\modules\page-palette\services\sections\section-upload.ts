import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";

interface Params {
  id?: string;
  content: { title: string; description: string };
}

export async function uploadSectionToServerSide({ id, content }: Params) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = id
      ? await PATCH(
          `/page-palette/landing-page/sections/${id}`,
          headers,
          content
        )
      : await POST(
          `/page-palette/landing-page/sections/register`,
          headers,
          content
        );

    return { ok: true, status: 200, error: "" };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadSectionToServerSide({ id, content })
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
