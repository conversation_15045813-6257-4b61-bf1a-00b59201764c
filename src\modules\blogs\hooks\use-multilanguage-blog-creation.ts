import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import cleanBlogCreationFormData from "../utils/form-data-cleaning/blog-creation-form";
import { validateBlogData } from "../validation/blogs/validate-blog-data";
import uploadBlogToServerSide from "../services/blog-upload";

export default function useMultilanguageBlogCreation(
  getMetaContent: () => MultilanguageSeoContentPayload
) {
  const t = useTranslations("warnings");
  const blogsT = useTranslations("BlogsManagement");

  const queryClient = useQueryClient();
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    if (language === blogsT("languages.arabic")) setActiveLanguage("arabic");
    else if (language === blogsT("languages.french"))
      setActiveLanguage("french");
    else if (language === blogsT("languages.english"))
      setActiveLanguage("english");
  };

  async function submitBlog(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanBlogCreationFormData(formData);
      validateBlogData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      await uploadBlogToServerSide(filteredFormData);

      queryClient.invalidateQueries({
        queryKey: ["blogs"],
        exact: false,
      });

      if (previousUrl && previousUrl.startsWith("/blogs"))
        router.push(previousUrl);
      else router.push("/blogs");
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitBlog,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
  };
}
