import { GET } from "@/lib/http-methods";
import { AxiosError } from "axios";
import { refreshToken } from "@auth/services/refresh-token";
import { UserDataType, UserResponseDataType } from "@auth/types";
import { castToUserType } from "@auth/utils/data-utils/types-casting/user";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";

export async function retrieveUserDetails(): Promise<UserDataType | null> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`${process.env.BACKEND_ADDRESS}/users/me`, header);
    const userData = res.data as UserResponseDataType;

    return castToUserType(userData);
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveUserDetails);

      return res;
    }

    return null;
  }
}
