import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { useResetPasswordContext } from "@auth/context/reset-password";
import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import LockIcon from "@/modules/auth/assets/icons/lock";
import { useAuthMode } from "../../store/auth-mode-store";

export default function CodeStep() {
  const t = useTranslations("shared.reset-password");

  const { warning, submitCode, displayedTimer, code, setCode, isLoading } =
    useResetPasswordContext();

  const { setMode: setAuthMode } = useAuthMode();

  return (
    <div className={"flex flex-col space-y-1"}>
      <Text textStyle="TS4" className="font-bold text-center text-black">
        {t.raw("title")}
      </Text>
      <Text textStyle="TS7" className="text-gray">
        <span>{t.raw("codeStep.description")}</span>
        <span className="text-white">{`(${displayedTimer})`}</span>
      </Text>
      <div className={"flex space-x-2 w-fit"}></div>

      <Text textStyle="TS7" className="w-full text-red">
        {warning.generalWarning}
      </Text>
      <div className="pt-4 flex flex-col space-y-4">
        <InputOTP
          maxLength={5}
          value={code}
          onChange={(value: string) => setCode(value)}
          pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
        >
          <InputOTPGroup className="w-full rounded-[15px] border border-green py-2 flex overflow-hidden text-green">
            <span className="w-1/6 px-3">
              <LockIcon key={"lock-icon"} />
            </span>
            {Array.from({ length: 5 }).map((_, idx) => (
              <div className="w-1/6 flex justify-center items-center" key={idx}>
                <InputOTPSlot
                  className={cn(
                    "w-fit min-w-4 px-1 py-1 border-b text-green ",
                    TextStyle["TS6"]
                  )}
                  index={idx}
                />
              </div>
            ))}
          </InputOTPGroup>
        </InputOTP>
        <Button
          className="rounded-[15px] h-11"
          onClick={() => submitCode(code)}
          disabled={code.length < 5 || isLoading}
        >
          <Text textStyle="TS7">{t.raw("codeStep.button")}</Text>
        </Button>

        {/* <div className="w-full flex justify-center">
          <Button variant="ghost" onClick={() => setStep("email")}>
            <Text textStyle="TS7" className="font-tajawal text-gray underline">
              {t.raw("return")}
            </Text>
          </Button>
        </div> */}
        <div className="w-full flex justify-center">
          <Button variant="ghost" onClick={() => setAuthMode("signIn")}>
            <Text textStyle="TS7" className="text-gray underline">
              {t.raw("comebackButton")}
            </Text>
          </Button>
        </div>
      </div>
    </div>
  );
}
