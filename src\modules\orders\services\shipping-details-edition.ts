import { PATCH } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

export default async function editShippingDetailsOnServerSide(
  shippingDetailsId: string,
  shippingDetails: { [key: string]: string }
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await PATCH(
      `/shippings/rate/${shippingDetailsId}`,
      header,
      shippingDetails
    );

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        editShippingDetailsOnServerSide(shippingDetailsId, shippingDetails)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
