import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import usePromotionUpload from "../hooks/use-promotion-upload";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import FormSubmission from "@/modules/catalog/components/form-submission";
import { formatDateTimeToInputFormat } from "../utils/date-formatting";

interface PromotionPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const promotionsNamesOnServerSide = ["BlackFriday", "VenteFlash", "Other"];

export function PromotionUpload({ isOpen, onClose }: PromotionPopupProps) {
  const {
    handleName<PERSON>hange,
    handleInputChange,
    handleSubmit,
    handleSwitchChange,
    promotionData,
    isPending,
    warning,
  } = usePromotionUpload(onClose);
  const t = useTranslations("PromotionsManagement.form.promotionUpload");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("labels.title")}</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <FormSubmission
          cancel={t("labels.cancelButton")}
          submit={t("labels.saveButton")}
          onCancel={onClose}
          onSubmit={handleSubmit}
          isPending={isPending}
          hideTopButtons
        >
          <div className="flex flex-col space-y-3">
            <Text textStyle="TS6" className="text-red">
              {warning}
            </Text>
            <div className="space-y-2">
              <Label htmlFor="name">{t("labels.promotionName")}</Label>
              <Input
                name="name"
                placeholder={t("labels.promotionName")}
                onChange={(event) => handleNameChange(event.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">
                {t("labels.promotionDescription")}
              </Label>
              <Textarea
                id="description"
                name="description"
                className={cn(TextStyle["TS7"])}
                value={promotionData.description}
                onChange={handleInputChange}
                placeholder={t("labels.promotionDescription")}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime">{t("labels.fromTime")}</Label>
                <Input
                  id="startTime"
                  name="from"
                  className={cn("block", TextStyle["TS7"])}
                  type="datetime-local"
                  value={formatDateTimeToInputFormat(
                    promotionData.from as string
                  )}
                  onChange={handleInputChange}
                  required={!promotionData.forever}
                  disabled={promotionData.forever}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endTime">{t("labels.toTime")}</Label>
                <Input
                  id="endTime"
                  name="to"
                  className={cn("block", TextStyle["TS7"])}
                  type="datetime-local"
                  value={formatDateTimeToInputFormat(
                    promotionData.to as string
                  )}
                  onChange={handleInputChange}
                  required={!promotionData.forever}
                  disabled={promotionData.forever}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isForever"
                checked={promotionData.forever}
                onCheckedChange={handleSwitchChange}
              />
              <Label htmlFor="isForever">{t("labels.forever")}</Label>
            </div>
          </div>
        </FormSubmission>
      </DialogContent>
    </Dialog>
  );
}
