import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import EditionIcon from "@assets/icons/management/edition-icon";
import { ShippingDetails } from "../../types/shipping-cost";
import LocationIcon from "@assets/icons/location-icon";

interface Props extends HTMLAttributes<"div"> {
  shipping: ShippingDetails;
  onEdit: () => void;
}

export default function ShippingDetailsContainer(props: Props) {
  return (
    <div
      className={cn("border-t rounded-md px-[14px] py-4 ", props.className, {
        "grid grid-cols-3": props.shipping.prices.length === 1,
        "grid grid-cols-4": props.shipping.prices.length === 2,
        "grid grid-cols-5": props.shipping.prices.length === 3,
        "flex justify-between items-center": props.shipping.prices.length > 3,
      })}
    >
      <div className="col-span-2 flex items-center space-x-3">
        <LocationIcon />
        <Text
          textStyle="TS5"
          className="text-black overflow-hidden overflow-ellipsis"
        >
          {props.shipping?.city?.name}
        </Text>
      </div>

      <div className="flex L:flex-row flex-col L:justify-between gap-2">
        {props.shipping.prices.map((shipping, idx) => (
          <Text
            key={idx}
            textStyle="TS5"
            className="text-black overflow-hidden overflow-ellipsis self-center"
          >
            {`{shipping.price} ${shipping.currency}`}
          </Text>
        ))}
      </div>

      <div className="flex-1 flex justify-end">
        <div
          onClick={() => {
            props.onEdit();
          }}
          className="cursor-pointer border rounded-md w-fit px-3 flex items-center rounded-s  space-x-2 active:scale-95 active:opacity-85 duration-300"
        >
          <EditionIcon color="#1E5EFF" />
        </div>
      </div>
    </div>
  );
}
