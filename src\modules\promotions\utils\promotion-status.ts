type PromotionStatus = "active" | "upcoming" | "expired";

export function getPromotionStatus(
  from: string | Date,
  to: string | Date
): PromotionStatus {
  const now = new Date().getTime();
  const startTime = new Date(from).getTime();
  const endTime = new Date(to).getTime();

  if (now < startTime) return "upcoming"; // Promotion hasn't started yet
  if (now >= startTime && now <= endTime) return "active"; // Promotion is ongoing

  return "expired"; // Promotion has ended
}
