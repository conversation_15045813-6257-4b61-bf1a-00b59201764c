import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import uploadPointsRateToServerSide from "../services/points-rate-upload";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";

interface Params {
  defaultRate: number;
}

export default function usePointsRateUpload({ defaultRate }: Params) {
  const generalWarningsContent = useTranslations("warnings");
  const t = useTranslations("pointsRateManagement");

  const [pointsRate, setPointsRate] = useState(defaultRate);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [successfullyChanged, setSuccessfullyChanged] = useState(false);

  const queryClient = useQueryClient();

  useEffect(() => {
    setPointsRate(defaultRate);
  }, [defaultRate]);

  async function submitPointsRate() {
    setIsPending(true);

    try {
      if (pointsRate < 0)
        throw new CustomError(
          "Negative Points Rate!",
          400,
          "negativePointsRate"
        );

      setWarning("");

      await uploadPointsRateToServerSide({ rate: pointsRate });

      queryClient.invalidateQueries({
        queryKey: ["points-rate"],
        exact: false,
      });

      toast({
        title: t("successfulChangement.title"),
        description: t("successfulChangement.description"),
      });

      setSuccessfullyChanged(true);

      setTimeout(() => {
        setSuccessfullyChanged(false);
      }, 2000);
    } catch (error) {
      const customError = error as CustomError;

      if (customError.status === 500) {
        setWarning(generalWarningsContent("serverError"));
        toast({
          title: generalWarningsContent("warning"),
          description: generalWarningsContent("serverError"),
        });
      } else if (customError.status === 400) {
        if (customError.code === "negativePointsRate") {
          setWarning(t("warnings.negativePointsRate.description"));
          toast({
            title: t("warnings.negativePointsRate.title"),
            description: t("warnings.negativePointsRate.description"),
          });
        } else {
          setWarning(generalWarningsContent("upload.invalidFormat"));
          toast({
            title: generalWarningsContent("warning"),
            description: generalWarningsContent("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    isPending,
    warning,
    submitPointsRate,
    pointsRate,
    setPointsRate,
    successfullyChanged,
  };
}
