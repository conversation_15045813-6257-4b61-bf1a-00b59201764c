{
  "recommendations": [
    "bradlc.vscode-tailwindcss", // Tailwind CSS IntelliSense for class names
    "dbaeumer.vscode-eslint", // Linting support for JavaScript/TypeScript
    "dsznajder.es7-react-js-snippets", // Snippets for React, Redux, and GraphQL
    "esbenp.prettier-vscode", // Code formatter for consistent styling
    "mgmcdermott.vscode-language-babel", // Syntax highlighting for JavaScript and JSX
    "naumovs.color-highlight", // Highlights colors in code (useful in styling React components)
    "oderwat.indent-rainbow", // Visual guide for nested JSX
    "rodrigovallades.es7-react-js-snippets", // Another React snippets extension
    "usernamehw.errorlens", // Highlights errors and warnings inline
    "yoavbls.pretty-ts-errors", // Improves TypeScript error messages for better debugging
    "ms-vsliveshare.vsliveshare", //live share extension
    "johnpapa.vscode-peacock", //Bar color editor
    "wayou.vscode-todo-highlight" //highlight to do extension
  ]
}
