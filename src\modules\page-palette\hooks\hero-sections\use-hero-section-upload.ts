import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { validatePaletteData } from "../../validation/brands/validate-palette-data";
import createHeroSectionOnServerSide from "../../services/hero-sections/addition";
import cleanHeroSectionFormData from "../../utils/form-data-cleaning/hero-section-cleaning";

interface Params {
  onCreate?: () => void;
}

export default function useHeroSectionUpload({ onCreate }: Params) {
  const [isPending, setIsPending] = useState(false);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);
  const t = useTranslations("warnings");
  const [warning, setWarning] = useState("");

  async function submitPalette(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    const formData = new FormData(formRef.current);

    try {
      const cleannedData = cleanHeroSectionFormData(formData);
      validatePaletteData(cleannedData);

      setWarning("");

      await createHeroSectionOnServerSide(cleannedData);

      if (onCreate) onCreate();
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitPalette,
    formRef,
    warning,
    isPending,
  };
}
