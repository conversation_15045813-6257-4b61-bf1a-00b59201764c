import WarnInput from "@/components/input/warn-input";
import TextEditor from "@/components/text-editor";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";

interface MultilanguageFormFieldsProps {
  multilanguage: boolean;
  language?: string;
  namePrefix?: string;
  initialName?: string;
  initialDescription?: string;
  onFieldChange?: () => void;
}

export default function MultilanguageFormFields({
  multilanguage,
  language = "",
  namePrefix = "",
  initialName = "",
  initialDescription = "",
  onFieldChange,
}: MultilanguageFormFieldsProps) {
  const uploadContent = useTranslations("shared.forms.upload");

  const getFieldName = (fieldName: string) => {
    if (multilanguage && language) {
      return `${namePrefix}${fieldName}_${language}`;
    }
    return fieldName;
  };

  const getFieldLabel = (fieldKey: string, required: boolean) => {
    if (multilanguage && language) {
      return `${uploadContent(
        `brandLabels.${fieldKey}`
      )} ${language} ${uploadContent(required ? "required" : "optional")} :`;
    }
    return `${uploadContent(`brandLabels.${fieldKey}`)} ${uploadContent(
      required ? "required" : "optional"
    )}`;
  };

  return (
    <div className="flex flex-col space-y-5">
      <div className="w-full flex flex-col space-y-2">
        <Label htmlFor={getFieldName("name")}>
          {getFieldLabel("name", true)}
        </Label>
        <WarnInput
          id={getFieldName("name")}
          name={getFieldName("name")}
          value={initialName}
          warning=""
          onChange={onFieldChange}
        />
      </div>

      <div className="w-full flex flex-col space-y-2">
        <Label htmlFor={getFieldName("description")}>
          {getFieldLabel("description", false)}
        </Label>
        <TextEditor
          name={getFieldName("description")}
          placeholder={uploadContent("brandLabels.description")}
          initialContent={initialDescription}
          className="w-full max-w-full"
        />
      </div>
    </div>
  );
}
