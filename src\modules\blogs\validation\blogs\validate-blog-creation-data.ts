import { CustomError } from "@/utils/custom-error";

export function validateBlogCreationData(formData: FormData) {
  const content = formData.get("content");
  const parsedContent = content ? JSON.parse(content.toString()) : [];

  if (!Array.isArray(parsedContent) || parsedContent.length === 0) {
    throw new CustomError("Missed Data!", 400);
  }

  parsedContent.forEach((item: any) => {
    if (!item.name || !item.description || !item.language) {
      throw new CustomError("Missed Data!", 400);
    }
  });
}

export function validateBlogEditionData(formData: FormData) {
  const name = formData.get("name");
  const description = formData.get("description");

  if (!name || !description) {
    throw new CustomError("Missed Data!", 400);
  }
}
