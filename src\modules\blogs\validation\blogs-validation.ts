import { CustomError } from "@/utils/custom-error";

export function validateBlogData(formData: FormData): void {
  // Basic required fields that are always needed
  const requiredFields: string[] = [
    "title",
    "details",
    "description",
    "metaContent",
    "categoryIds",
  ];
  const missingFields: string[] = [];

  // Check basic required fields
  requiredFields.forEach((field) => {
    if (!formData.has(field) || formData.get(field) === "") {
      missingFields.push(field);
    }
  });

  formData.delete("deletedImages");
  formData.delete("imageDeleted");

  if (missingFields.length > 0) {
    throw new CustomError(`Missed Data!`, 400);
  }

  const displayOrder = formData.get("displayOrder");
  if (displayOrder !== null && typeof displayOrder === "string") {
    if (parseFloat(displayOrder) < 1) {
      throw new CustomError(`Invalid Data`, 400);
    }
  }
}
