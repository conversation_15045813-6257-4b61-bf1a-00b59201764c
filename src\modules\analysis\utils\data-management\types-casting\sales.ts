import {
  SalesAnalysisInResponse,
  SalesAnalysisType,
} from "@/modules/analysis/types/sales";

export function castToSalesAnalysis(
  salesInResponse: SalesAnalysisInResponse
): SalesAnalysisType {
  return {
    averageRevenue: Number(salesInResponse.averageRevenue),
    totalRevenue: Number(salesInResponse.totalRevenue),
    totalQuantitySold: Number(salesInResponse.totalOrders),
    dailyRevenue: salesInResponse.dailyRevenue.map((details) => {
      return {
        revenue: Number(details.totalRevenue),
        currency: details.currency,
        date: new Date(details.date),
      };
    }),
  };
}
