import { useTranslations } from "next-intl";
import { Dispatch, SetStateAction } from "react";
import CustomizableDialog from "@/components/customizable-dialog";
import PaletteCreationDialog from "./addition-dialog";
import { useQueryClient } from "@tanstack/react-query";

interface Props {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  close: () => void;
}

export function HeroSectionAddition(props: Props) {
  const t = useTranslations("PagePalette.HeroSections");
  const queryClient = useQueryClient();

  return (
    <CustomizableDialog
      setIsOpen={props.setIsOpen}
      isOpen={props.isOpen}
      close={props.close}
      title={t("title")}
    >
      <PaletteCreationDialog
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: ["palettes"],
          });

          props.close();
        }}
        onCancel={props.close}
      />
    </CustomizableDialog>
  );
}
