import { z } from "zod";

const EnglishPasswordConfirmationSchema = z
  .object({
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
    confirmationPassword: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
  })
  .refine((data) => data.password === data.confirmationPassword, {
    message: "Passwords do not match",
    path: ["confirmationPassword", "generalWarning"],
  });

const FrenchPasswordConfirmationSchema = z
  .object({
    password: z.string().min(8, {
      message: "Le mot de passe doit contenir au moins 8 caractères.",
    }),
    confirmationPassword: z.string().min(8, {
      message: "Le mot de passe doit contenir au moins 8 caractères.",
    }),
  })
  .refine((data) => data.password === data.confirmationPassword, {
    message: "Les mots de passe ne correspondent pas.",
    path: ["confirmationPassword", "generalWarning"],
  });

export function getPasswordConfirmationSchema() {
  return FrenchPasswordConfirmationSchema;
}
