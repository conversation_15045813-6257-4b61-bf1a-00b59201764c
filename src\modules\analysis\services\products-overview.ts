import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { ProductsOverviewInResponse } from "../types/catalog";
import { castToProductsOverviewType } from "../utils/data-management/types-casting/catalog";

export default async function retrieveProductsOverview() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET("/statistics/products/overview", headers);

    return castToProductsOverviewType(res.data as ProductsOverviewInResponse);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveProductsOverview);

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
