import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { validateBrandEditionData } from "../../validation/brands/validate-brand-edition-data";
import { updateBrandLanguageContent } from "../../services/brands/brand-language-update";
import { cleanBrandEditionFormData } from "../../utils/form-data-cleaning/brand-edition-form";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";

export default function useMultilanguageBrandEdition(
  getMetaContent: () => MultilanguageSeoContentPayload,
  getBrandId: () => string,
  onSuccessfulUpdate: () => void,
  onSlugChange?: (newSlug: string) => void
) {
  const t = useTranslations("warnings");
  const successT = useTranslations("shared.forms.upload");
  const brandsT = useTranslations("BrandsManagement");

  const queryClient = useQueryClient();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState<
    "arabic" | "french" | "english"
  >("french");
  const [formKey, setFormKey] = useState(0);

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    setFormKey((prev) => prev + 1);
    setActiveLanguage(language);
  };

  async function submitBrand(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanBrandEditionFormData(
        formData,
        activeLanguage
      );
      validateBrandEditionData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      const brandId = getBrandId();
      const response = await updateBrandLanguageContent(
        filteredFormData,
        brandId,
        activeLanguage
      );

      queryClient.invalidateQueries({
        queryKey: ["brands"],
        exact: false,
      });

      toast({
        title: successT("successTitle"),
        description: successT("successDescription"),
      });

      if (response.data && response.data.slug && onSlugChange) {
        onSlugChange(response.data.slug);
      }

      onSuccessfulUpdate();
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitBrand,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
    formKey,
  };
}
