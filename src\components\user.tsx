import Text from "@/styles/text-styles";
import VerifiedIcon from "@assets/icons/verified";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import SquareArrowRight from "@assets/icons/square-arrow-right";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import useUser from "@/modules/auth/hooks/use-user";
import { logout } from "@/modules/auth/utils/log-out";

export default function User() {
  const { user } = useUser();
  const t = useTranslations("shared.navbar");
  const router = useRouter();

  return (
    user && (
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center justify-end L:space-x-3 M:space-x-2 space-x-1 outline-none">
          <div className="border rounded-[10px] h-10 w-10 relative flex justify-center items-center">
            <div className="absolute -right-[5px] -top-[5px]">
              <VerifiedIcon />
            </div>
          </div>
          <div className="flex flex-col spacep-y-1">
            <Text textStyle="TS7" className="font-bold text-black">
              {user?.name}
            </Text>

            <Text textStyle="TS8" className="font-bold text-gray">
              {user?.role === "Admin"
                ? t(`accountRole.admin`)
                : t(`accountRole.contentManager`)}
            </Text>
          </div>
          <ChevronDown />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="">
          <DropdownMenuItem
            onClick={() => logout(router)}
            className="L:min-w-[200px] M:min-w-[150px] px-4 py-2 bg-white flex space-x-3 cursor-pointer"
          >
            <SquareArrowRight color="#FF0000" />
            <Text textStyle="TS7">{t("logOut")}</Text>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  );
}
