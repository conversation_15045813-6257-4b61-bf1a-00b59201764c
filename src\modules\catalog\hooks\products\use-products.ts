import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import retrieveProducts from "@/modules/catalog/services/products/products-extraction";
import { ProductType } from "../../types/products";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import useUrlParams from "@/hooks/urls-management/use-url-params";
import { CriteriaType } from "../../types";
import { usePathname } from "next/navigation";

export default function useProducts(limit: number) {
  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination({ paginationAffectUrl: true });
  const { updateUrlParams, getParamFromUrl, removeParamFromUrl } =
    useUrlParams();
  const pathname = usePathname();

  const [searchedProducts, setSearchedProducts] = useState("");
  const [searchBy, setSearchBy] = useState("");
  const [criteria, setCriteria] = useState<CriteriaType | "">("");
  const [searchVersion, setSearchVersion] = useState(0);

  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<{
    data: ProductType[];
    pagination: PaginationType;
  }>({
    queryKey: [
      "product",
      "products",
      user,
      page,
      searchedProducts,
      searchBy,
      criteria,
    ],
    queryFn: () =>
      retrieveProducts(page, limit, searchedProducts, searchBy, criteria),
    enabled: user !== null,
    placeholderData: (prev) => prev,
  });

  //pages update once data is fetched
  useEffect(() => {
    if (
      data &&
      data.data &&
      data.pagination &&
      pagesNumber !== data.pagination?.totalPages
    ) {
      setPagesNumber(data.pagination.totalPages);
      setRecords(data.data.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  //get the searched products and ordered from the url
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchedProductsInUrl = getParamFromUrl("search");
      const criteriaInUrl = getParamFromUrl("criteria");

      if (searchedProductsInUrl) setSearchedProducts(searchedProductsInUrl);
      if (criteriaInUrl) {
        setCriteria(criteriaInUrl as CriteriaType);
      }
    }
  }, [pathname]);

  //update the url when the searched products change
  useEffect(() => {
    if (searchedProducts !== "") {
      setPage(1);

      updateUrlParams("search", searchedProducts);
    } else {
      const searchInUrl = getParamFromUrl("search");

      if (searchInUrl) removeParamFromUrl("search");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchVersion]);

  //update the url when the criteria of products changed
  useEffect(() => {
    if (criteria !== "") {
      updateUrlParams("criteria", criteria);
    } else {
      const criteriaInUrl = getParamFromUrl("criteria");

      if (criteriaInUrl) removeParamFromUrl("criteria");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [criteria]);

  const searchProducts = (text: string) => {
    setSearchedProducts(text);
    setSearchVersion(searchVersion + 1);
  };

  return {
    products: data?.data,
    productsAreLoading: isLoading,
    productsError: isError,
    setPage,
    page,
    records,
    pagesNumber,
    searchProducts,
    searchedProducts,
    setSearchBy,
    setCriteria,
    criteria,
    searchBy,
  };
}
