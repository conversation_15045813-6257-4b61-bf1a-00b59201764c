import { PATCH } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";

export async function updateCategoryLanguageContent(
  elementData: FormData,
  elementId: string,
  language: string
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/categories/${elementId}?language=${language}`;

    const response = await PATCH(
      `${process.env.BACKEND_ADDRESS}${endpoint}`,
      header,
      elementData
    );

    return {
      ok: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        updateCategoryLanguageContent(elementData, elementId, language)
      );

      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }
    throw new CustomError("Server Error!", 500);
  }
}
