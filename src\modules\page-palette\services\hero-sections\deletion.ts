import { POST } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";

type Response = {
  status: number;
  ok: boolean;
  error: string;
};

export async function deleteHeroSectionOnServerSide(elementData: {
  computerImage: string;
  mobileImage: string;
}): Promise<Response> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
    "Content-Type": "application/json",
  };

  try {
    const endpoint = `/page-palette/landing-page/images/delete`;

    await POST(
      `${process.env.BACKEND_ADDRESS}${endpoint}`,
      headers,
      JSON.stringify(elementData)
    );

    return { ok: true, status: 200, error: "" };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        deleteHeroSectionOnServerSide(elementData)
      );

      // Unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
