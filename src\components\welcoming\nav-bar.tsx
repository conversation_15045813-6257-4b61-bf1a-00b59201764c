"use client";

import Text from "@/styles/text-styles";
import { NavButton } from "../buttons/nav-button";
import Image from "next/image";
import { useTranslations } from "next-intl";

const references = [
  "https://www.tawer.tn#projectsSection",
  "https://www.tawer.tn#bookAMeetingSection",
  "https://www.tawer.tn#servicesSection",
];

export default function WelcomingNavbar() {
  const t = useTranslations("welcoming.navBar");

  return (
    <div className="w-full h-[60px] XL:px-[40px] extraL:px-[20px] sm:px-[40px] px-[15px] py-[10px] flex items-center justify-between">
      <Image
        src="/logos/tawer-logo.svg"
        width={163}
        height={35}
        className="extraTinySm:w-[163px] w-[133px] h-[35px]"
        alt="Company compact Logo"
      />
      <div className="flex-1 extraL:flex hidden justify-center  xl:space-x-[73px] lg:space-x-[60px] md:space-x-[30px] XL:text-[20pt] extraL:text-[17pt] text-[20px] ">
        {t.raw("elements").map((navBarElement: string, index: number) => (
          <NavButton writing="left" link={references[index]} key={index}>
            <Text className="navBarElementHovering text-purple" textStyle="TS4">
              {navBarElement}
            </Text>
          </NavButton>
        ))}
      </div>
    </div>
  );
}
