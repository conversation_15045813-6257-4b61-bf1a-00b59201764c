export default function TrashBin({ color = "white" }: { color?: string }) {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.8889 9.55371C18.8889 17.5728 20.0432 21.1975 12.2794 21.1975C4.51466 21.1975 5.69276 17.5728 5.69276 9.55371"
        fill={color}
      />
      <path
        d="M20.3653 6.47961H4.21484"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.7138 6.47958C15.7138 6.47958 16.2424 2.71387 12.2881 2.71387C8.3348 2.71387 8.86337 6.47958 8.86337 6.47958"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
