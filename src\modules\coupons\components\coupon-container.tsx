"use client";
import Text from "@/styles/text-styles";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CouponType } from "../types/coupons";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { formatDate } from "@/modules/promotions/utils/date-formatting";

type Props = {
  coupon: CouponType;
};

export default function CouponContainer({ coupon }: Props) {
  const t = useTranslations("CouponsManagement");
  const { currency } = useCurrency();

  return (
    <ScrollArea className="max-w-[400px]">
      <div className="flex flex-col justify-between space-y-4 rounded-2xl border border-lightGray p-4 bg-white">
        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("couponsInfo.labels.code")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {coupon.code}
          </Text>
          {coupon.description && (
            <>
              <Text textStyle="TS6" className="font-bold block">
                {t("couponsInfo.labels.description")}
              </Text>
              <Text textStyle="TS6" className="block font-thin">
                {coupon.description}
              </Text>
            </>
          )}
        </div>

        <div className="flex flex-col space-y-2">
          <Text textStyle="TS6" className="font-bold block">
            {t("couponsInfo.labels.type")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {coupon.discount
              ? coupon.discount.type === "amount"
                ? t("fixedDiscount")
                : t("percentageDiscount")
              : t("freeShipping")}
          </Text>
        </div>

        {coupon.discount && (
          <div className="flex flex-col space-y-2">
            <Text textStyle="TS6" className="font-bold block">
              {t("couponsInfo.labels.discount")}
            </Text>
            <Text textStyle="TS6" className="block font-thin">
              {coupon.discount.type == "percentage"
                ? `${coupon.discount.value * 100} %`
                : `${coupon.discount.value} ${currency}`}
            </Text>
          </div>
        )}

        <Separator />

        <div className="flex flex-col space-y-3">
          <Text textStyle="TS6" className="font-bold block">
            {t("couponsInfo.labels.validity")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {coupon.period.forever
              ? t("couponsInfo.labels.forever")
              : `${formatDate(coupon.period.from)} - ${formatDate(
                  coupon.period.to
                )}`}
          </Text>
        </div>

        <Separator />

        <div className="flex flex-col space-y-3">
          <Text textStyle="TS6" className="font-bold block">
            {t("couponsInfo.labels.usageLimits")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {coupon.unlimitedUsage
              ? t("couponsInfo.labels.unlimited")
              : `${t("couponsInfo.labels.maxRedemptions")}: ${
                  coupon.maxRedemptions !== undefined
                    ? coupon.maxRedemptions
                    : ""
                }`}
          </Text>
          {coupon.maxUsesPerUser !== undefined && (
            <Text textStyle="TS6" className="block font-thin">
              {`${t("couponsInfo.labels.maxUsesPerUser")}: ${
                coupon.maxUsesPerUser
              }`}
            </Text>
          )}
          <Text textStyle="TS6" className="block font-thin">
            {`${t("couponsInfo.labels.totalRedemptions")}: ${
              coupon.totalVoucherRedemptionsByUsers
            }`}
          </Text>
        </div>

        <Separator />

        <div className="flex flex-col space-y-3">
          <Text textStyle="TS6" className="font-bold block">
            {t("couponsInfo.labels.restrictions")}
          </Text>
          <Text textStyle="TS6" className="block font-thin">
            {t(
              coupon.appliesToAllProducts
                ? "couponsInfo.labels.appliesToAllProducts"
                : "couponsInfo.labels.appliesToSpecificProducts"
            )}
          </Text>
          {!coupon.appliesToAllProducts &&
            coupon.productItemIds &&
            coupon.productItemIds.length > 0 && (
              <Text textStyle="TS6" className="block font-thin">
                {`${t(
                  "couponsInfo.labels.productItemIds"
                )}: ${coupon.productItemIds.join(", ")}`}
              </Text>
            )}
          {coupon.allowOnPromotions !== undefined && (
            <Text textStyle="TS6" className="block font-thin">
              {`${t("couponsInfo.labels.allowOnPromotions")}: ${
                coupon.allowOnPromotions
                  ? t("couponsInfo.labels.yes")
                  : t("couponsInfo.labels.no")
              }`}
            </Text>
          )}
          {coupon.freeShipping !== undefined && (
            <Text textStyle="TS6" className="block font-thin">
              {`${t("couponsInfo.labels.freeShipping")}: ${
                coupon.freeShipping
                  ? t("couponsInfo.labels.yes")
                  : t("couponsInfo.labels.no")
              }`}
            </Text>
          )}
        </div>

        <Separator />

        <div className="flex flex-col space-y-2 w-fit">
          <Text textStyle="TS6" className="font-bold block">
            {t("couponsInfo.labels.status")}
          </Text>
          {coupon?.status === "active" ? (
            <div className="rounded-sm bg-light-green text-green flex items-center justify-center p-1 px-3 w-fit">
              <Text textStyle="TS7">
                {t("couponsInfo.couponStatus.active")}
              </Text>
            </div>
          ) : coupon?.status === "expired" ? (
            <div className="rounded-sm bg-lightGray text-gray flex items-center justify-center p-1 px-3 w-fit">
              <Text textStyle="TS7">
                {t("couponsInfo.couponStatus.expired")}
              </Text>
            </div>
          ) : (
            <div className="rounded-sm bg-light-red text-red flex items-center justify-center p-1 px-3 w-fit">
              <Text textStyle="TS7">
                {t("couponsInfo.couponStatus.inActive")}
              </Text>
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  );
}
