import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import retrieveProductsOverview from "../services/products-overview";
import { ProductsOverview } from "../types/catalog";

export default function useProductsOverview() {
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<ProductsOverview>({
    queryKey: ["products-number-overview", user],
    queryFn: retrieveProductsOverview,
    enabled: user !== null,
  });

  return {
    data,
    isLoading,
    isError,
  };
}
