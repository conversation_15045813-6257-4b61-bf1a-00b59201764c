import { HTMLAttributes, useEffect, useState } from "react";
import Text from "@/styles/text-styles";
import Image from "next/image";
import { Checkbox } from "@/components/ui/checkbox";
import { CatalogElementType } from "../../types";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";

interface Props extends HTMLAttributes<"div"> {
  catalog: {
    name: string;
    id: string;
    description?: string;
    image: string | null;
  };
  type: CatalogElementType;
  onEdit: () => void;
  addCatalogToDeletionList: (catalogId: string) => void;
  removeCatalogFromDeletionList: (catalogId: string) => void;
}

export default function BrandContainer({ catalog, type, ...props }: Props) {
  const [brandImage, setBrandImage] = useState("/not-found/image.png");

  useEffect(() => {
    if (catalog.image) setBrandImage(catalog.image);
  }, [catalog]);

  const handleDeletionCheck = (checked: boolean) => {
    if (checked) {
      props.addCatalogToDeletionList(catalog.id);
    } else props.removeCatalogFromDeletionList(catalog.id);
  };

  return (
    <div className="flex items-center justify-between p-2 regularL:p-4 border rounded-lg hover:bg-lightGray group">
      <div className="flex items-center gap-3 regularL:gap-4">
        <Checkbox
          onCheckedChange={handleDeletionCheck}
          className="text-blue outline-none bg-white w-5 h-5 flex-shrink-0"
        />

        <Image
          alt="Catalog image"
          src={brandImage}
          objectFit="cover"
          width={48}
          height={48}
          className="rounded-md border flex-shrink-0"
          unoptimized
          onError={() => setBrandImage("/not-found/image.png")}
        />
        <Text textStyle="TS6" className="regularL:font-medium w-1/2">
          {catalog.name}
        </Text>
      </div>
      <div className="flex items-center gap-2 transition-opacity">
        <div className="flex flex-col regularL:flex-row gap-1">
          <Button
            variant="outline"
            size="icon"
            onClick={() => {
              props.onEdit();
            }}
            className="h-8 w-8"
          >
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
