export default function cleanCouponFormData(
  formData: FormData,
  elementId?: string
): FormData {
  const filteredFormData = new FormData();
  const categoryIds: string[] = [];

  formData.forEach((value, key) => {
    if (key === "displayOrder") {
      //posting display order if user change it
      if ((value as string).trim() !== "") filteredFormData.append(key, value);
    } else {
      if (key === "categoryIds") {
        categoryIds.push(value as string);
      } else if (
        !elementId && //coupon creation
        (value as string).trim() !== ""
      ) {
        filteredFormData.append(key, value);
      } else if (elementId) {
        //coupon edition
        filteredFormData.append(key, value);
      }
    }
  });

  categoryIds.forEach((id) => filteredFormData.append("categoryIds", id));

  return filteredFormData;
}
