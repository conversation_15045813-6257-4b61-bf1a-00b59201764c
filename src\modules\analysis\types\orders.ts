export interface OrdersOverviewInResponse {
  currency: string;
  averageRevenueCurrentMonth: number;
  averageRevenueLastMonth: number;
  totalRevenueCurrentMonth: number;
  totalRevenueLastMonth: number;
  totalOrdersCurrentMonth: number;
  totalOrdersLastMonth: number;
}

export interface OrdersOverview {
  currency: string;
  averageRevenueCurrentMonth: number;
  averageRevenueLastMonth: number;
  totalRevenueCurrentMonth: number;
  totalRevenueLastMonth: number;
  totalOrdersCurrentMonth: number;
  totalOrdersLastMonth: number;
}
