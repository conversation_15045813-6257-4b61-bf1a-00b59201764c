import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { PaletteType } from "../types/palette";
import { PaginationType } from "@/types/pagination";
import { retrievePagePaletteFromServerSide } from "../services/palette-extraction";

export default function usePagePalette() {
  const { data, isLoading, isError } = useQuery<{
    palettes: PaletteType;
    pagination: PaginationType;
  } | null>({
    queryKey: ["palettes"],
    queryFn: retrievePagePaletteFromServerSide,
    placeholderData: keepPreviousData,
  });

  return {
    palettes: data && data.palettes ? data?.palettes : null,
    palettesAreLoading: isLoading,
    palettesError: isError,
  };
}
