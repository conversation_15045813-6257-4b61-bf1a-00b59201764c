import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CategoryInResponseType } from "@catalog/types/categories";
import { castToCategoryType } from "@catalog/utils/data-management/types-casting/categories";

export default async function retrieveCategories() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/categories/dashboard`;

    const res = await GET(endpoint, headers);
    return (res.data as CategoryInResponseType[]).map((categoryInResposne) =>
      castToCategoryType(categoryInResposne)
    );
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveCategories);

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}

export async function retrieveCategory(categoryId: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/categories/${categoryId}`;

    const res = await GET(`${endpoint}`, headers);

    return castToCategoryType(res.data as CategoryInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveCategory(categoryId));

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
