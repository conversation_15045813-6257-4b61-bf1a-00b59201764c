import WarnInput from "@/components/input/warn-input";
import { useResetPasswordContext } from "@/modules/auth/context/reset-password";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { useAuthMode } from "../../store/auth-mode-store";
import Email from "@/modules/auth/assets/icons/email";

export default function EmailStep() {
  const t = useTranslations("shared.reset-password");
  const { warning, submitEmail, email, setEmail, isLoading } =
    useResetPasswordContext();
  const { setMode: setAuthMode } = useAuthMode();

  return (
    <div className="flex flex-col items-center justify-center space-y-1 text-black">
      <Text textStyle="TS4" className="font-bold text-center text-black">
        {t.raw("title")}
      </Text>
      <Text textStyle="TS7" className="pt-5 text-gray text-center">
        {t.raw("emailStep.description")}
      </Text>
      <Text textStyle="TS7" className="w-full text-red">
        {warning.generalWarning}
      </Text>
      <div className="w-full pt-4 flex flex-col space-y-3">
        <div className="relative w-full">
          <WarnInput
            id="email"
            className="rounded-[15px] border border-gray pl-10"
            warning={warning.email}
            value={email}
            onChange={(event: any) => setEmail(event.target.value)}
            placeholder={t.raw("emailStep.email")}
          />
          <Email />
        </div>
        <Button
          className={cn("rounded-[15px]", { "opacity-70": isLoading })}
          onClick={submitEmail}
          disabled={isLoading}
        >
          <Text textStyle="TS7">{t.raw("emailStep.button")}</Text>
        </Button>

        <div className="w-full flex justify-center">
          <Button variant="ghost" onClick={() => setAuthMode("signIn")}>
            <Text textStyle="TS7" className="text-gray underline">
              {t.raw("comebackButton")}
            </Text>
          </Button>
        </div>
      </div>
    </div>
  );
}
