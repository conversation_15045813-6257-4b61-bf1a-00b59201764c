import type { Metadata } from "next";
import "./globals.css";
import { notoSerifFontVar, tajawalFontVar } from "@/styles/fonts";
import ReactQueryProvider from "@/utils/providers/react-query-provider";
import { getLocale, getMessages } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
import { cn } from "@/lib/utils";

export const metadata: Metadata = {
  title:
    "Tawer Board – Tableau de Bord Puissant pour Gérer Parastore E-commerce",
  description:
    "Optimisez la gestion de votre boutique Parastore grâce à Tawer Board : un tableau de bord complet pour suivre les ventes, les produits, les clients et les performances en temps réel.",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html lang={locale.substring(0, 2)}>
      <body className={cn("bg-white", tajawalFontVar, notoSerifFontVar)}>
        <NextIntlClientProvider messages={messages}>
          <ReactQueryProvider>{children}</ReactQueryProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
