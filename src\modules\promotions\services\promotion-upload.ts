import { POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import { UploadedPromotionType } from "../types/promotions";

export default async function uploadPromotionOnServerSide(
  promotionData: UploadedPromotionType
) {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    await POST(`/promotions/register`, header, promotionData);

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorCode = (
      axiosError.response?.data as { message: string; code: string }
    )?.code;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadPromotionOnServerSide(promotionData)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else if (axiosError.response?.status === 400)
      throw new CustomError("Invalid Data!", 400, errorCode);

    throw new CustomError("Server Error!", 500);
  }
}
