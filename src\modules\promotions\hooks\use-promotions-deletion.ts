import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { deletePromotionOnServerSide } from "../services/promotion-deletion";

export default function usePromotionsDeletion() {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);
  const [promotionId, setPromotionId] = useState("");

  const requestPromotionDeletion = (promotionId: string) => {
    setPromotionId(promotionId);
    setAlertModalIsOpen(true);
  };

  const deletePromotion = async () => {
    setIsPending(true);

    try {
      await deletePromotionOnServerSide(promotionId);

      if (warning !== "") setWarning("");

      setAlertModalIsOpen(false);

      queryClient.invalidateQueries({
        queryKey: ["promotions"],
      });
    } catch (throwedError) {
      const error = throwedError as CustomError;
      if (error.status === 500) setWarning(t("serverError"));
    }

    setIsPending(false);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
    setPromotionId("");
  };

  return {
    isPending,
    alertModalIsOpen,
    cancelDeletion,
    warning,
    deletePromotion,
    requestPromotionDeletion,
  };
}
