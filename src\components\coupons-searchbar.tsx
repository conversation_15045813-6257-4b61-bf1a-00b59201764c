import { Dispatch, SetStateAction } from "react";
import { Input } from "./ui/input";
import { useTranslations } from "next-intl";
import { Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { TextStyle } from "@/styles/text-styles";

interface Props {
  setSearchedContent: Dispatch<SetStateAction<string>>;
  searchedContent: string;
}

export default function CouponsSearchbar(props: Props) {
  const t = useTranslations("shared.searchBar");
  return (
    <div className="overflow-hidden rounded-md border w-full regularL:w-fit flex items-center px-3 bg-white">
      <Search className="text-gray mr-2" size={16} />
      <Input
        value={props.searchedContent}
        onChange={(e) => props.setSearchedContent(e.target.value)}
        className={cn(
          "bg-transparent border-none outline-none p-0 flex-1",
          TextStyle["TS6"]
        )}
        placeholder={t("placeholder")}
      />
    </div>
  );
}
