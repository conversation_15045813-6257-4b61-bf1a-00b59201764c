"use client";
import { useTranslations } from "next-intl";
import OrderManagementContainer from "./order/order-management-container";
import PaginationMangement from "@/components/pagination/pagination-management";
import useOrders from "../hooks/use-orders";
import OrderContainer from "./order/order-container";
import CustomizableDialog from "@/components/customizable-dialog";
import { useEffect, useState } from "react";
import { Order } from "../types/orders";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import Searchbar from "@/components/searchbar";

export default function OrdersManagementList() {
  const t = useTranslations("ordersManagement");
  const {
    orders,
    ordersAreLoading,
    pagesNumber,
    currentPage,
    changePage,
    records,
    setSearchedOrders,
    searchedOrders,
  } = useOrders(10);

  const [order, setOrder] = useState<Order | null>(null);

  const handleModalClosing = () => {
    setOrder(null);
  };

  useEffect(() => {
    if (order && orders) {
      const editedOrder = orders.find(
        (iteratedOrder) => iteratedOrder.id === order.id
      );

      if (editedOrder) setOrder(editedOrder);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orders]);

  return (
    <DashboardListsContainer
      title={t("title")}
      parentClassName="border-0"
      includesSearchbar={true}
      headerElement={
        <div className="flex justify-between items-center space-x-5 pe-5">
          <Searchbar
            searchedContent={searchedOrders}
            setSearchedContent={setSearchedOrders}
            placeHolder={t("searchPlaceHolder")}
          />
        </div>
      }
    >
      {!(ordersAreLoading || orders === undefined) ? (
        <div className="overflow-x-auto flex flex-col space-y-3">
          {orders && orders.length > 0 ? (
            orders.map((order) => (
              <OrderManagementContainer
                onClick={() => {
                  setOrder(order);
                }}
                key={order.id}
                order={order}
              />
            ))
          ) : (
            <NoDataFound className="py-5 flex justify-center items-center" />
          )}

          {pagesNumber > 1 ? (
            <div className="pt-3 px-3">
              <PaginationMangement
                records={records}
                pagesNumber={pagesNumber}
                currentPage={currentPage}
                changePage={changePage}
              />
            </div>
          ) : null}
          <CustomizableDialog
            title={t("orderInfo.title")}
            setIsOpen={(open) => {
              if (!open) {
                setOrder(null);
              }
            }}
            isOpen={order !== null}
            close={handleModalClosing}
          >
            {order && <OrderContainer order={order} />}
          </CustomizableDialog>
        </div>
      ) : (
        <DashboardListsContainerSkeleton>
          <div className="flex-1 flex flex-col gap-2">
            {Array.from({ length: 10 }).map((_, idx) => (
              <Skeleton key={idx} className="h-14 w-full" />
            ))}
          </div>
        </DashboardListsContainerSkeleton>
      )}
    </DashboardListsContainer>
  );
}
