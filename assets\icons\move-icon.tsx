import { cn } from "@/lib/utils";

interface Props {
  direction: "top" | "left" | "right" | "bottom";
  color?: string;
}
export default function MoveIcon({ color = "#7E84A3", direction }: Props) {
  return (
    <svg
      width="15"
      height="12"
      viewBox="0 0 15 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn({
        "rotate-90": direction === "top",
        "rotate-180": direction === "right",
        "-rotate-90": direction === "bottom",
      })}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.3871 0.209705L5.29289 0.292893L0.292893 5.29289C-0.0675907 5.65338 -0.0953203 6.22061 0.209705 6.6129L0.292893 6.70711L5.29289 11.7071C5.68342 12.0976 6.31658 12.0976 6.70711 11.7071C7.06759 11.3466 7.09532 10.7794 6.7903 10.3871L6.70711 10.2929L3.414 6.999L14 7C14.5523 7 15 6.55228 15 6C15 5.44772 14.5523 5 14 5L3.416 4.999L6.70711 1.70711C7.06759 1.34662 7.09532 0.779392 6.7903 0.387101L6.70711 0.292893C6.34662 -0.0675907 5.77939 -0.0953203 5.3871 0.209705Z"
        fill={color}
      />
    </svg>
  );
}
