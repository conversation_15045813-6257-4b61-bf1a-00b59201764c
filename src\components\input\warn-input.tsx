"use client";

import { cn } from "@/lib/utils";
import { InputHTMLAttributes, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { InputWarning } from "./input-warning";
import EyeIcon from "@assets/icons/eye-icon";
import { TextStyle } from "@/styles/text-styles";

interface WarnInputPropsType extends InputHTMLAttributes<HTMLInputElement> {
  warning?: string;
  eyeColor?: string;
}
export default function WarnInput({
  warning = "",
  className = "",
  type,
  id,
  eyeColor = "#4000ef",
  ...props
}: WarnInputPropsType) {
  const [passwordType, setPasswordType] = useState("password");
  const [value, setValue] = useState(props.value ? props.value : "");

  useEffect(() => {
    setValue(props.value ? props.value : "");
  }, [props.value]);

  return (
    <div className="relative flex items-center">
      <Input
        id={id}
        className={cn(
          "text-sm",
          {
            "border-[1px] border-red": warning && warning !== "",
          },
          className,
          TextStyle["TS6"]
        )}
        type={type === "password" ? passwordType : type}
        {...props}
        value={value}
        onChange={(event) => setValue(event.target.value)}
      />

      <div className={cn("absolute flex items-center space-x-2 right-3")}>
        {type === "password" ? (
          <div
            className="cursor-pointer h-4 w-5 flex items-center justify-center active:opacity-70"
            onClick={() =>
              setPasswordType(passwordType === "password" ? "text" : "password")
            }
          >
            <EyeIcon color={eyeColor} />
          </div>
        ) : null}
        <InputWarning warning={warning} />
      </div>
    </div>
  );
}
