import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import updateOrderStatusOnServerSide from "../services/order-status-edition";
import { OrderStatus } from "../types/orders";

export default function useOrderStatusEdition(onEdited: () => void) {
  const queryClient = useQueryClient();
  const [warning, setWarning] = useState("");
  const t = useTranslations("warnings");
  const { isPending, mutate } = useMutation<
    unknown,
    CustomError,
    { orderId: string; status: OrderStatus }
  >({
    mutationFn: ({ orderId, status }) =>
      updateOrderStatusOnServerSide(orderId, status),
    onSuccess: () => {
      if (warning !== "") setWarning("");

      queryClient.invalidateQueries({
        queryKey: ["orders"],
        exact: false,
      });

      onEdited();
    },
    onError: (error) => {
      if (error.status === 400) setWarning(t("upload.missedData"));
      else setWarning(t("serverError"));
    },
  });

  const updateOrderStatus = (orderId: string, status: OrderStatus) => {
    mutate({ orderId, status });
  };

  return {
    isPending,
    updateOrderStatus,
    warning,
    orderStatusList: [
      "Processing",
      "Delivered",
      "Cancelled",
      "Returned",
      "Failed",
    ] as OrderStatus[],
  };
}
