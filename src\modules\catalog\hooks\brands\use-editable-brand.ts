import useUser from "@/modules/auth/hooks/use-user";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { BrandType } from "@/modules/catalog/types/brands";
import { retrieveEditableBrand } from "@/modules/catalog/services/brands/editable-brand-extraction";

interface BrandProps {
  brandSlug: string;
  language?: string;
}

export default function useEditableBrand({ brandSlug, language }: BrandProps) {
  const { user } = useUser();
  const pathname = usePathname();
  const { data, isLoading, refetch } = useQuery<BrandType>({
    queryKey: [brandSlug, user, pathname, language],
    queryFn: () => retrieveEditableBrand(brandSlug, language),
    placeholderData: keepPreviousData,
    enabled: user !== null,
  });
  return {
    isLoading,
    brand: data as BrandType,
    refetch,
  };
}
