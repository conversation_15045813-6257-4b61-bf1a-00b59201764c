import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import useUser from "@/modules/auth/hooks/use-user";
import { CustomError } from "@/utils/custom-error";
import { deleteElementtOnServerSide } from "../services/element-deletion";
import { useTranslations } from "next-intl";
import { CatalogElementType } from "../types";

export default function useElementsDeletion(
  type: CatalogElementType,
  parentElementId?: string
) {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const [elementsIds, setElementsIds] = useState<string[]>([]);
  const { user } = useUser();
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);
  const deleteAllElements = async () => {
    setIsPending(true);

    try {
      await Promise.all(
        elementsIds.map((elementId) =>
          deleteElementtOnServerSide(type, elementId)
        )
      );

      if (warning !== "") setWarning("");

      queryClient.invalidateQueries({
        queryKey:
          type === "product"
            ? ["product", "products", user]
            : type === "category"
            ? ["categories"]
            : type === "brand"
            ? ["brands"]
            : type === "coupon"
            ? ["coupon"]
            : ["product", parentElementId],
        exact: false,
      });

      setAlertModalIsOpen(false);
      setElementsIds([]);
    } catch (throwedError) {
      const error = throwedError as CustomError;
      if (error.status === 500) setWarning(t("serverError"));
    }

    setIsPending(false);
  };

  const onDelete = () => {
    if (elementsIds.length > 0) setAlertModalIsOpen(true);
  };

  const addElementToDeletionList = (elementId: string) => {
    setElementsIds([...elementsIds, elementId]);
  };

  const removeElementFromDeletionList = (elementId: string) => {
    const filterList = elementsIds.filter((id) => elementId !== id);
    setElementsIds(filterList);
  };

  const requireDirectDeletion = (elementId: string) => {
    setElementsIds([elementId]);
    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
  };

  return {
    isPending,
    alertModalIsOpen,
    cancelDeletion,
    warning,
    deleteAllElements,
    setElementsIds,
    addElementToDeletionList,
    removeElementFromDeletionList,
    onDelete,
    elementsIds,
    requireDirectDeletion,
  };
}
