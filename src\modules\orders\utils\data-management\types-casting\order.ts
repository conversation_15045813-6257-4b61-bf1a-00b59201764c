import { Order, OrderResponseDataType } from "@/modules/orders/types/orders";

export function castToOrderType(order: OrderResponseDataType): Order {
  const couponCodeDiscount = calcuateCouponCodeDiscount(order);

  return {
    createdAt: order.createdAt,
    id: order.id,
    code: order.code,
    total: Number(order.total) + couponCodeDiscount,
    shippingCost: Number(order.shippingCost),
    currency: order.currency,
    status: order.status,
    address: {
      firstName: order.address.firstName,
      lastName: order.address.lastName,
      address1: order.address.address1,
      address2: order.address.address2,
      company: order.address.company,
      zone: order.address.zone,
      postalCode: order.address.postalCode,
      phone: order.address.phone,
      city: {
        name: order.address.city.name,
        country: {
          name: order.address.city.country.name,
          isoCode: order.address.city.country.isoCode,
        },
      },
    },
    couponCodeDiscount,
    items: order.items.map((orderItem) => {
      return {
        couponCode: orderItem.voucherCode,
        image: orderItem.image
          ? `${process.env.BACKEND_ADDRESS}${orderItem.image}`
          : "/not-found/product-image.webp",
        name: orderItem.name,
        quantity: orderItem.quantity,
        size: orderItem.size,
        price: orderItem.price ? Number(orderItem.price) : orderItem.price,
        barcode: orderItem.barcode,
      };
    }),
  };
}

function calcuateCouponCodeDiscount(order: OrderResponseDataType) {
  const totalDiscount = order.items.reduce(
    (sum, item) =>
      item.voucherCode
        ? sum +
          (item.voucherCode.discount.type === "amount"
            ? Number(item.voucherCode.discount.value)
            : Number(item.voucherCode.discount.value) * Number(item.price))
        : sum,
    0
  );
  return totalDiscount;
}
