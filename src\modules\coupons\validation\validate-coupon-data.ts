import { CustomError } from "@/utils/custom-error";
import { UploadedCouponType } from "../types/coupons";

export function validateCouponData(couponData: UploadedCouponType): void {
  const requiredFields: (keyof UploadedCouponType)[] = ["code"];
  const missingFields: (keyof UploadedCouponType)[] = [];

  for (const field of requiredFields) {
    if (typeof couponData[field] === "string" && !couponData[field]) {
      missingFields.push(field);
    } else if (
      typeof couponData[field] === "number" &&
      (couponData[field] === null ||
        couponData[field] === undefined ||
        isNaN(couponData[field]))
    ) {
      missingFields.push(field);
    } else if (
      Array.isArray(couponData[field]) &&
      couponData[field].length === 0
    ) {
      missingFields.push(field);
    }
  }

  if (missingFields.length > 0) {
    throw new CustomError("Missed Data!", 400);
  }

  // validate maxUsesPerUser
  if ((couponData.maxUsesPerUser ?? 0) < 1) {
    throw new CustomError(
      "Le nombre maximal d'utilisations est obligatoire.",
      400
    );
  }

  //  Shipping and Discount validation
  if (couponData.freeShipping) {
    delete couponData.discount;
  } else {
    // When not free shipping, validate discount
    if (!couponData.discount) {
      throw new CustomError("discountRequired", 400);
    }
    if (
      couponData.discount.value === undefined ||
      couponData.discount.value === null ||
      couponData.discount.value <= 0
    ) {
      throw new CustomError("invalidDiscountValue", 400);
    }
  }

  // Validate discount percentage if present
  if (
    couponData.discount?.type === "percentage" &&
    typeof couponData.discount.value === "number"
  ) {
    const value = couponData.discount.value;
    if (value >= 0 && value <= 1) {
      couponData.discount.value = value;
    } else if (value >= 0 && value <= 100) {
      couponData.discount.value = value / 100;
    } else {
      throw new CustomError("invalidPercentageValue", 400);
    }
  }

  // unlimited usage
  if (couponData.unlimitedUsage) {
    delete couponData.maxRedemptions;
  } else {
    // When not unlimited usage, maxRedemptions is required
    if (!couponData.maxRedemptions || couponData.maxRedemptions < 1) {
      throw new CustomError("maxRedemptionsRequired", 400);
    }
    // Validate maxRedemptions against maxUsesPerUser
    if (
      couponData.maxUsesPerUser &&
      couponData.maxRedemptions < couponData.maxUsesPerUser
    ) {
      throw new CustomError("maxRedemptionsLessThanMaxUsesPerUser", 400);
    }
  }

  if (couponData.period.forever) {
    delete couponData.period.from;
    delete couponData.period.to;
  } else {
    if (!couponData.period.from || !couponData.period.to) {
      throw new CustomError("missingPeriodDates", 400);
    }

    const fromDate = new Date(couponData.period.from);
    const toDate = new Date(couponData.period.to);
    const now = new Date();

    // Validate dates are valid
    if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
      throw new CustomError("invalidDateFormat", 400);
    }

    // Validate date range
    if (fromDate >= toDate) {
      throw new CustomError("invalidDateRange", 400);
    }
  }
}
