"use client";

import { Home, ShoppingBag, PlusCircle, BadgePercent } from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import Insights from "@assets/icons/sidebar/insights";
import Traffic from "@assets/icons/sidebar/traffic";
import Intelligence from "@assets/icons/sidebar/intelligence";
import CouponCode from "@assets/icons/sidebar/coupon-code";
import Cube from "@assets/icons/sidebar/cube";
import Image from "next/image";
import { useTranslations } from "next-intl";

// Menu items.
const mainMenu = [
  {
    key: "home-sidebar-button",
    pathname: "/",
    icon: Home,
  },
  {
    key: "orders-sidebar-button",
    pathname: "/orders",
    icon: ShoppingBag,
  },
  // {
  //   key: "shipping-details-sidebar-button",
  //   pathname: "/shipping-details",
  //   icon: Car,
  // },
  {
    key: "promotions-sidebar-button",
    pathname: "/promotions",
    icon: BadgePercent,
  },
  {
    key: "coupon-code-sidebar-button",
    pathname: "/coupons",
    icon: CouponCode,
  },
  {
    key: "customers-sidebar-button",
    pathname: "/customers",
    icon: Traffic,
  },
  {
    key: "points-conversion-rate-sidebar-button",
    pathname: "/points-rate",
    icon: Traffic,
  },
  {
    key: "insights-sidebar-button",
    pathname: "/insights",
    icon: Insights,
  },
  {
    key: "traffic-sidebar-button",
    pathname: "/traffic",
    icon: Traffic,
  },
  {
    key: "intelligence-sidebar-button",
    pathname: "/intelligence",
    icon: Intelligence,
  },
];

const categoriesMenu = [
  {
    key: "view-categories-sidebar-button",
    pathname: "/categories",
    icon: Cube,
  },
];

const contentMenu = [
  {
    key: "hero-section-sidebar-button",
    pathname: "/content/hero-section",
    icon: PlusCircle,
  },
];

const blogsMenu = [
  {
    key: "add-blog-sidebar-button",
    pathname: "/blogs/addition",
    icon: PlusCircle,
  },
  {
    key: "view-blogs-sidebar-button",
    pathname: "/blogs",
    icon: Cube,
  },
];

const productsMenu = [
  {
    key: "add-product-sidebar-button",
    pathname: "/products/addition",
    icon: PlusCircle,
  },
  {
    key: "view-products-sidebar-button",
    pathname: "/products",
    icon: Cube,
  },
];

const brandsMenu = [
  {
    key: "add-brand-sidebar-button",
    pathname: "/brands/addition",
    icon: PlusCircle,
  },
  {
    key: "view-brands-sidebar-button",
    pathname: "/brands",
    icon: Cube,
  },
];

const landingPagePaletteMenu = [
  {
    key: "landing-page-palette-sidebar-button",
    pathname: "/page-palette/hero-sections",
    icon: Cube,
  },
  {
    key: "landing-page-palette-sidebar-button",
    pathname: "/page-palette/seo",
    icon: PlusCircle,
  },
  {
    key: "landing-page-palette-sidebar-button",
    pathname: "/page-palette/content",
    icon: PlusCircle,
  },
];

const enabledPathnames = [
  "/",
  "/orders",
  "/promotions",
  "/shipping-details",
  "/coupons",
  "/customers",
  "/points-rate",
];

export function AppSidebar() {
  const pathname = usePathname();
  const t = useTranslations("shared.sideBar");
  const { isMobile } = useSidebar();

  return (
    <Sidebar className="pt-7 px-7 bg-white">
      <div
        className={cn("border-b pb-5 flex justify-between items-center", {
          "px-3 pt-4": isMobile,
        })}
      >
        <Image
          alt="tawer-company-logo"
          src={"/logos/tawer-logo.svg"}
          width={146}
          height={33}
        />
        {isMobile ? <SidebarTrigger /> : null}
      </div>
      <SidebarContent
        className={cn({
          "mt-10": !isMobile,
        })}
      >
        <SidebarGroup>
          <SidebarGroupLabel>{t("mainMenu.title")}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainMenu.map((item, idx) => (
                <SidebarMenuItem key={t.raw("mainMenu.menu")[idx]}>
                  <SidebarMenuButton
                    asChild
                    className={cn("", {
                      "bg-purple bg-opacity-[23%]": pathname === item.pathname,
                    })}
                    id={item.key}
                  >
                    <a
                      href={item.pathname}
                      aria-disabled={!enabledPathnames.includes(item.pathname)}
                    >
                      <item.icon />
                      <span>{t.raw("mainMenu.menu")[idx]}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel>{t("contentMenu.title")}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {landingPagePaletteMenu.map((item, idx) => (
                <SidebarMenuItem key={t.raw("contentMenu.menu")[idx]}>
                  <SidebarMenuButton
                    asChild
                    className={cn("", {
                      "bg-purple bg-opacity-[23%]": pathname === item.pathname,
                    })}
                    id={item.key}
                  >
                    <a href={item.pathname}>
                      <item.icon />
                      <span>{t.raw("contentMenu.menu")[idx]}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>{t("categoriesMenu.title")}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {categoriesMenu.map((item, idx) => (
                <SidebarMenuItem key={t.raw("categoriesMenu.menu")[idx]}>
                  <SidebarMenuButton
                    asChild
                    className={cn("", {
                      "bg-purple bg-opacity-[23%]": pathname === item.pathname,
                    })}
                    id={item.key}
                  >
                    <a href={item.pathname}>
                      <item.icon />
                      <span>{t.raw("categoriesMenu.menu")[idx]}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel>{t("blogsMenu.title")}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {blogsMenu.map((item, idx) => (
                <SidebarMenuItem key={t.raw("blogsMenu.menu")[idx]}>
                  <SidebarMenuButton
                    asChild
                    className={cn("", {
                      "bg-purple bg-opacity-[23%]": pathname === item.pathname,
                    })}
                    id={item.key}
                  >
                    <a href={item.pathname}>
                      <item.icon />
                      <span>{t.raw("blogsMenu.menu")[idx]}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel>{t("brandsMenu.title")}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {brandsMenu.map((item, idx) => (
                <SidebarMenuItem key={t.raw("brandsMenu.menu")[idx]}>
                  <SidebarMenuButton
                    asChild
                    className={cn("", {
                      "bg-purple bg-opacity-[23%]": pathname === item.pathname,
                    })}
                    id={item.key}
                  >
                    <a href={item.pathname}>
                      <item.icon />
                      <span>{t.raw("brandsMenu.menu")[idx]}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel>{t("productsMenu.title")}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {productsMenu.map((item, idx) => (
                <SidebarMenuItem key={t.raw("productsMenu.menu")[idx]}>
                  <SidebarMenuButton
                    asChild
                    className={cn("", {
                      "bg-purple bg-opacity-[23%]": pathname === item.pathname,
                    })}
                    id={item.key}
                  >
                    <a href={item.pathname}>
                      <item.icon />
                      <span>{t.raw("productsMenu.menu")[idx]}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
