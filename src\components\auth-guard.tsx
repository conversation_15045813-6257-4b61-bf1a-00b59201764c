"use client";
import useUser from "@/modules/auth/hooks/use-user";
import { LandingPageSkeletons } from "@/templates/landing-page";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

export default function AuthGuard(props: Props) {
  const { user, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && (user === null || (user && !user.isAuthenticated)))
      router.push("/");

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isLoading]);

  return !(!user || isLoading) ? (
    <>{props.children}</>
  ) : (
    <LandingPageSkeletons />
  );
}
