import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import Navbar from "@/components/navbar";
import TawerBar from "@/components/tawer-bar";
import { Toaster } from "@/components/ui/toaster";
import AuthGuard from "@/components/auth-guard";

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AuthGuard>
      <SidebarProvider>
        <AppSidebar />
        <main className="relative w-full">
          <Navbar />
          <div className="flex-1 mt-[75px] bg-LightAzure">
            <TawerBar />
            <div className="extraL:px-10 M:px-5 px-2 min-h-[100vh]">
              {children}
            </div>
          </div>
          <Toaster />
        </main>
      </SidebarProvider>
    </AuthGuard>
  );
}
