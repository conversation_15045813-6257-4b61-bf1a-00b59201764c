import FormSubmission from "@/modules/catalog/components/form-submission";
import { useTranslations } from "next-intl";
import ImageUpload from "../../image-upload";
import Text from "@/styles/text-styles";
import { Input } from "@/components/ui/input";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import useHeroSectionUpload from "@/modules/page-palette/hooks/hero-sections/use-hero-section-upload";

interface Props {
  onCancel: () => void;
  onSuccess: () => void;
}

export default function HeroSectionAdditionDialog({
  onCancel,
  onSuccess,
}: Props) {
  const {
    formRef,
    submitPalette,
    warning,
    isPending: uploadingIsPending,
  } = useHeroSectionUpload({ onCreate: onSuccess });
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("shared.forms.upload.file");

  return (
    <FormSubmission
      cancel={uploadContent("cancel")}
      submit={uploadContent("create")}
      onCancel={onCancel}
      onSubmit={submitPalette}
      isPending={uploadingIsPending}
      hideTopButtons
    >
      <form ref={formRef} className="flex flex-col space-y-3 overflow-y-auto">
        <div className=" rounded-2xl border border-lightGray p-7 bg-white space-y-5">
          <div>
            <Text textStyle="TS8" className="text-blue">
              Note
            </Text>
            <Text textStyle="TS8" className="text-gray">
              : {t("photoFormat")}
            </Text>
          </div>
          <div className="text-red">{warning}</div>

          <div className="flex flex-col space-y-5">
            <div className="flex flex-col space-y-4">
              <Text textStyle="TS7" className="text-gray mt-4">
                {uploadContent("url")}
              </Text>
              <Input
                type="url"
                name={"redirectUrl"}
                placeholder="https://www.example.com"
              />
            </div>

            <div className="flex flex-col rounded-lg border-lg border-purple">
              <Text textStyle="TS7" className="text-gray">
                {uploadContent("pc")}
              </Text>
              <ImageUpload name="computerImage" />
            </div>

            <div className="flex flex-col">
              <Text textStyle="TS7" className="text-gray">
                {uploadContent("mobile")}
              </Text>
              <ImageUpload name="mobileImage" />
            </div>

            {/* Display Order */}
            <div className="w-full flex flex-col space-y-4">
              <Text textStyle="TS7" className="text-gray">
                {uploadContent("productLabels.displayOrder")}
              </Text>
              <WarnInput
                name="displayOrder"
                type="number"
                onWheel={disableScrollOnNumberInput}
                warning=""
                placeholder={uploadContent("productLabels.displayOrder")}
              />
            </div>
          </div>
        </div>
      </form>
    </FormSubmission>
  );
}
