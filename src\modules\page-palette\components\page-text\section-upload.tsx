import { useState, useCallback, useRef } from "react";
import type React from "react";
import { Label } from "@/components/ui/label";
import TextEditor from "@/components/text-editor";
import { Save, AlertTriangle } from "lucide-react";
import TrashIcon from "@assets/icons/management/trash-icon";
import { useTranslations } from "next-intl";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Text from "@/styles/text-styles";
import { v4 as uuidv4 } from "uuid";

interface SectionEditorProps {
  index: number;
  initialContent?: { title: string; description: string; id: string };
  onSave: (param: {
    id?: string;
    title: string;
    description: string;
    descriptionIsEmpty?: boolean;
  }) => Promise<boolean>;
  onDelete: (id: string) => Promise<boolean>;
  deletionIsPending?: boolean;
  deletionWarning?: string;
  uploadWarning?: string;
  onSuccess?: () => void;
}

export default function PagePaletteSectionContentUpload({
  index,
  initialContent,
  onSave,
  onDelete,
  deletionIsPending,
  deletionWarning,
  uploadWarning,
  onSuccess,
}: SectionEditorProps) {
  const t = useTranslations("PagePalette.ContentManagement");

  const id = useRef(initialContent ? initialContent.id : uuidv4()).current;
  const [title, setTitle] = useState(
    initialContent ? initialContent.title : ""
  );
  const [content, setContent] = useState(
    initialContent ? initialContent.description : ""
  );

  const [unsaveChangement, setUnsaveChangement] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [descriptionIsEmpty, setDescriptionIsEmpty] = useState(true);

  const handleTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setTitle(e.target.value);
      setUnsaveChangement(true);
    },
    []
  );

  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    setUnsaveChangement(true);
  }, []);

  const handleSaveClick = async () => {
    setIsSaving(true);

    try {
      const isSaved = await onSave(
        initialContent
          ? { id, title, description: content, descriptionIsEmpty }
          : { title, description: content, descriptionIsEmpty }
      );

      if (isSaved) {
        setUnsaveChangement(false);

        //resetting form when it is a registration
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch {
      // Error handling is done via the uploadWarning prop
    } finally {
      setIsSaving(false);
    }
  };

  const openDeletionAlert = () => {
    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
  };

  return (
    <>
      <div className="w-full flex flex-col space-y-4 p-5 border border-gray-200 rounded-lg bg-gray-50">
        <div className="w-full flex justify-between items-center border-b border-gray-200 pb-3">
          <Label htmlFor={`section-${index}`} className="text-lg font-semibold">
            {t("section.label", {
              index: index + 1,
            })}
          </Label>
          <div className="flex gap-2">
            <Button
              onClick={handleSaveClick}
              disabled={!unsaveChangement || isSaving}
              className={`h-9 ${
                unsaveChangement ? "bg-green" : "bg-gray"
              } text-white rounded-[20px] px-2 py-1 M:px-4 M:py-2 flex items-center gap-2 active:opacity-80 active:scale-95 duration-300 disabled:opacity-50 disabled:cursor-not-allowed`}
              id={`save-section-button-${index}`}
            >
              <Save size={16} />
              <Text textStyle="TS6" className="hidden M:inline">
                {isSaving ? t("button.saving") : t("button.save")}
              </Text>
            </Button>
            <Button
              onClick={openDeletionAlert}
              className="h-9 bg-red text-white rounded-[20px] px-2 py-1 M:px-4 M:py-2 flex items-center gap-2 active:opacity-80 active:scale-95 duration-300"
              id={`delete-palette-button-${index}`}
            >
              <TrashIcon />
            </Button>
          </div>
        </div>

        <div className="mb-4">
          <Label
            htmlFor={`section-title-${index}`}
            className="block mb-2 font-semibold"
          >
            {t("section.titleLabel")}
          </Label>
          <Input
            type="text"
            id={`section-title-${index}`}
            value={title}
            onChange={handleTitleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={t("section.titlePlaceholder")}
          />
          <Text textStyle="TS7" className="text-gray italic flex items-center">
            <span className="mr-1">ℹ️</span> {t("section.titleNote")}
          </Text>
        </div>

        <div>
          <Label
            htmlFor={`section-content-${index}`}
            className="block mb-2 font-semibold"
          >
            {t("section.contentLabel")}
          </Label>
          <TextEditor
            name={`section-content-${index}`}
            initialContent={content}
            onChange={handleContentChange}
            setContentIsEmpty={setDescriptionIsEmpty}
          />
        </div>

        {uploadWarning && (
          <div className="flex flex-col gap-2 mt-2">
            {unsaveChangement && (
              <div className="text-amber-500 text-xs italic">
                {t("section.unsavedChanges")}
              </div>
            )}

            {/* Display upload warning if available */}

            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
              <AlertTriangle className="text-amber-500 h-5 w-5 mt-0.5 flex-shrink-0" />
              <Text textStyle="TS7" className="text-amber-700">
                {uploadWarning}
              </Text>
            </div>
          </div>
        )}
      </div>

      {/* Deletion confirmation dialog */}
      <AlertDialog open={alertModalIsOpen}>
        <ModalDialog
          title={t("deletionDialog.title", { fallback: "Confirm Deletion" })}
          details={t("deletionDialog.sectionDeletionDetails", {
            section: title || `Section ${index + 1}`,
          })}
          cancel={t("deletionDialog.cancel")}
          confirm={t("deletionDialog.confirm")}
          theme="red"
          warning={deletionWarning}
          isPending={deletionIsPending as boolean}
          onCancel={cancelDeletion}
          onConfirm={() => onDelete(id)}
        />
      </AlertDialog>
    </>
  );
}
