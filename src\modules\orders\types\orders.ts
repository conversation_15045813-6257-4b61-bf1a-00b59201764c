export type OrderStatus =
  | "Processing"
  | "Delivered"
  | "Cancelled"
  | "Returned"
  | "Failed";

export interface OrderResponseDataType {
  id: string;
  code: string;
  total: number;
  shippingCost: number;
  currency: string;
  status: OrderStatus;
  address: {
    firstName: string;
    lastName: string;
    address1: string;
    address2: string | null;
    company: string | null;
    zone: string | null;
    postalCode: string | null;
    phone: string;
    city: {
      name: string;
      country: {
        name: string;
        isoCode: string;
      };
    };
  };
  items: OrderItemTypeInResponse[];
  createdAt: string;
}

interface OrderItemTypeInResponse {
  name: string;
  quantity: number;
  size: number;
  price: number;
  image: string | null;
  voucherCode: VoucherCodeInResponse | null;
  barcode: string;
}

interface VoucherCodeInResponse {
  code: string;
  discount: {
    type: "percentage" | "amount";
    value: number;
  };
}

export interface Order {
  id: string;
  code: string;
  total: number;
  shippingCost: number;
  currency: string;
  status: OrderStatus;
  address: {
    firstName: string;
    lastName: string;
    address1: string;
    address2: string | null;
    company: string | null;
    zone: string | null;
    postalCode: string | null;
    phone: string;
    city: {
      name: string;
      country: {
        name: string;
        isoCode: string;
      };
    };
  };
  couponCodeDiscount: number;
  items: OrderItem[];
  createdAt: string;
}

export interface OrderItem {
  name: string;
  quantity: number;
  size: number;
  price: number;
  image: string;
  couponCode: VoucherCode | null;
  barcode: string;
}

interface VoucherCode {
  code: string;
  discount: {
    type: "percentage" | "amount";
    value: number;
  };
}
