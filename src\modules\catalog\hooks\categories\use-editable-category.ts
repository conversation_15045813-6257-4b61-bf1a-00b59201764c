import useUser from "@/modules/auth/hooks/use-user";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { CategoryType } from "@/modules/catalog/types/categories";
import { retrieveEditableCategory } from "@/modules/catalog/services/categories/editable-category-extraction";

interface UseEditableCategoryParams {
  categorySlug: string;
  language?: string;
}

export default function useEditableCategory(
  params: UseEditableCategoryParams | string
) {
  const { user } = useUser();
  const pathname = usePathname();

  const categorySlug =
    typeof params === "string" ? params : params.categorySlug;
  const language = typeof params === "string" ? undefined : params.language;

  const { data, isLoading, refetch } = useQuery<CategoryType>({
    queryKey: ["category", categorySlug, language, user, pathname],
    queryFn: () => retrieveEditableCategory(categorySlug, language),
    placeholderData: keepPreviousData,
    enabled: user !== null,
  });
  return {
    elementIsLoading: isLoading,
    element: data as CategoryType,
    refetch,
  };
}
