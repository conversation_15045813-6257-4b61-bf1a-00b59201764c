import { Language } from "@/modules/seo/types/multilanguage-seo";
import Text from "@/styles/text-styles";

interface LanguageTabsProps {
  options: { key: Language; value: string }[];
  onSelect: (value: string) => void;
  selectedValue: string;
}

export default function LanguageTabs({
  options,
  onSelect,
  selectedValue,
}: LanguageTabsProps) {
  return (
    <div className="flex items-end justify-center gap-6">
      {options.map((option) => (
        <div key={option.key} className="relative">
          <button
            type="button"
            onClick={() => onSelect(option.key)}
            className={`px-4 py-2 ${
              selectedValue === option.key
                ? "text-blue border-b-2 border-blue"
                : "text-gray"
            }`}
          >
            <Text textStyle="TS6">{option.value}</Text>
          </button>
          {selectedValue === option.key && (
            <div className="absolute bottom-0 left-0 w-full bg-blue"></div>
          )}
        </div>
      ))}
    </div>
  );
}
