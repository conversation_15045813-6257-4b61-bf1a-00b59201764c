import { PromotionType } from "../types/promotions";
import { useEffect, useState } from "react";
import usePromotions from "./use-promotions";

export default function usePromotion(promotionSlug: string) {
  const [promotion, setPromotion] = useState<PromotionType | null>(null);
  const { promotions } = usePromotions();

  useEffect(() => {
    if (promotions) {
      const searchedPromotion = promotions.find((promotion) => {
        return promotion.slug === promotionSlug;
      });

      if (searchedPromotion) setPromotion(searchedPromotion);
    }
  }, [promotions, promotionSlug]);

  return { promotion };
}
