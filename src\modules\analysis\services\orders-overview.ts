import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { castToOrdersOverview } from "../utils/data-management/types-casting/orders";
import { OrdersOverviewInResponse } from "../types/orders";
import { logger } from "@/utils/logger";

export default async function retrieveOrdersOverview() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET("/statistics/orders/overview", headers);

    return castToOrdersOverview(res.data as OrdersOverviewInResponse);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveOrdersOverview);

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
