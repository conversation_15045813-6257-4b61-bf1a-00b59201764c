import {
  CouponInResponseType,
  CouponType,
} from "@/modules/coupons/types/coupons";

export function castToCouponType(
  couponInResponse: CouponInResponseType
): CouponType {
  return {
    id: couponInResponse.id,
    description: couponInResponse.description,
    maxRedemptions: couponInResponse.maxRedemptions,
    maxUsesPerUser: couponInResponse.maxUsesPerUser,
    code: couponInResponse.code,
    productItemIds: couponInResponse.productItemIds,
    appliesToAllProducts: couponInResponse.appliesToAllProducts,
    allowOnPromotions: couponInResponse.allowOnPromotions,
    freeShipping: couponInResponse.freeShipping,
    discount: couponInResponse.discount ?? undefined,
    period: couponInResponse.period,
    totalVoucherRedemptionsByUsers:
      couponInResponse.totalVoucherRedemptionsByUsers,
    unlimitedUsage: couponInResponse.unlimitedUsage,
    status: couponInResponse.status,
  };
}
