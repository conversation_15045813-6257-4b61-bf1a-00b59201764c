import { CustomError } from "@/utils/custom-error";

export default function validateDiscountInput(id: string) {
  const discount = document.getElementById(id) as HTMLInputElement;

  if (discount) {
    const discountValue = Number(discount.value);

    if (discountValue <= 100 && discountValue >= 0) return discountValue;

    throw new CustomError("Invalid Data!", 400);
  }

  return new CustomError("Missed Data!", 400);
}
