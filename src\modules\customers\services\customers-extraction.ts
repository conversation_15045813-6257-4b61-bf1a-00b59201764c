import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CustomerInResponseType } from "../types/customers";
import { castToCustomerType } from "../utils/data-management/type-casting/customers";
import { PaginationType } from "@/types/pagination";

export default async function retrieveCustomers(
  page: number,
  limit: number,
  searchedCustomers: string,
  selectedFilter: string
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const searchQuery =
      searchedCustomers?.trim() && selectedFilter !== "none"
        ? `&${selectedFilter}=${searchedCustomers}`
        : "";

    const endpoint = `/users/dashboard?page=${page}&limit=${limit}${searchQuery}`;

    const res = await GET(endpoint, headers);
    return {
      data: (res.data.data as CustomerInResponseType[]).map(
        (customerInResponse) => castToCustomerType(customerInResponse)
      ),
      pagination: res.data.pagination as PaginationType,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveCustomers(page, limit, searchedCustomers, selectedFilter)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}

export async function retrieveCustomer(customerId: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/customer/${customerId}`;

    const res = await GET(`${endpoint}`, headers);

    return castToCustomerType(res.data as CustomerInResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveCustomer(customerId));

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
