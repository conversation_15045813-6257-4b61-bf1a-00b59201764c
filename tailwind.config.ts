import type { Config } from "tailwindcss";

const {
  default: flattenColorPalette,
  // eslint-disable-next-line @typescript-eslint/no-require-imports
} = require("tailwindcss/lib/util/flattenColorPalette");

export default {
  darkMode: ["class"],
  content: [
    "./assets/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/utils/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/sections/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/templates/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/styles/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/catalog/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/auth/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/orders/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/analysis/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/promotions/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/coupons/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/page-palette/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/seo/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/customers/components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // Your existing extensions...
      typography: {
        DEFAULT: {
          css: {
            maxWidth: "100%",
          },
        },
      },
      animation: {
        scroll:
          "scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite",
        "error-shake": "shakeAnimation 0.5s ease-in-out",
      },
      keyframes: {
        shakeAnimation: {
          "0%": {
            transform: "translate(0px, 1px) rotate(0deg)",
          },
          "10%": {
            transform: "translate(-1px, 0px) rotate(-1deg)",
          },
          "20%": {
            transform: "translate(-2px, -1px) rotate(0deg)",
          },
          "30%": {
            transform: "translate(-3px, 0px) rotate(1deg)",
          },
          "40%": {
            transform: "translate(-2px, 1px) rotate(0deg)",
          },
          "50%": {
            transform: "translate(-1px, 0px) rotate(-1deg)",
          },
          "60%": {
            transform: "translate(0px, -1px) rotate(0deg)",
          },
          "70%": {
            transform: "translate(1px, 0px) rotate(1deg)",
          },
          "80%": {
            transform: "translate(2px, 1px) rotate(0deg)",
          },
          "90%": {
            transform: "translate(3px, 0px) rotate(-1deg)",
          },
          "100%": {
            transform: "translate(0px, -1px) rotate(0deg)",
          },
        },
        scroll: {
          to: {
            transform: "translate(calc(-50% - 0.5rem))",
          },
        },
      },
      boxShadow: {
        input: `0px 2px 3px -1px rgba(0,0,0,0.1), 0px 1px 0px 0px rgba(25,28,33,0.02), 0px 0px 0px 1px rgba(25,28,33,0.08)`,
      },
      colors: {
        purple: "#4000ef",
        "dark-blue": "#131523",
        gray: "#777777",
        lightGray: "#D7DBEC",
        blue: "#1E5EFF",
        green: "#00A611",
        "light-green": "#C4F8E2",
        "light-blue": "#ECF2FF",
        red: "#FF0000",
        "light-red": "#FFDDDD",
        LightAzure: "#F7F9FC",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        tajawal: ["var(--font-tajawal)"],
        noto_serif: ["var(--font-noto_serif)"],
      },
      screens: {
        S: "350px",
        M: "450px",
        L: "600px",
        tinyL: "700px",
        regularL: "740px",
        extraL: "850px",
        "2L": "881px",
        "2extraL": "900px",
        "3L": "950px",
        XL: "1200px",
        extraXL: "1330px",
        "2XL": "1600px",
      },
    },
  },
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
} satisfies Config;

function addVariablesForColors({ addBase, theme }: any) {
  const allColors = flattenColorPalette(theme("colors"));
  const newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
  );

  addBase({
    ":root": newVars,
  });
}
