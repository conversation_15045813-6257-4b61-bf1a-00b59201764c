import { HTMLAttributes } from "react";
import Text from "@/styles/text-styles";
import Image from "next/image";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Edit } from "lucide-react";
import { BlogType } from "../types/blogs";

interface Props extends HTMLAttributes<"div"> {
  blog: BlogType;
  onEdit: () => void;
  addBlogToDeletionList: (blogId: string) => void;
  removeBlogFromDeletionList: (blogId: string) => void;
}

export default function BlogManagementContainer({ blog, ...props }: Props) {
  const handleDeletionCheck = (checked: boolean) => {
    if (checked) {
      props.addBlogToDeletionList(blog.id);
    } else props.removeBlogFromDeletionList(blog.id);
  };

  return (
    <div className="flex items-center justify-between p-2 regularL:p-4 border rounded-lg hover:bg-lightGray group">
      <div className="flex items-center gap-3 regularL:gap-4">
        <Checkbox
          onCheckedChange={handleDeletionCheck}
          className="text-blue outline-none bg-white w-5 h-5 flex-shrink-0"
        />
        <div className="w-[80px] h-[60px] rounded-lg overflow-hidden flex-shrink-0">
          <Image
            alt={"blog thumbnail"}
            src={blog.image || "/not-found/image.png"}
            width={80}
            height={60}
            className="object-cover w-full h-full"
            unoptimized
          />
        </div>

        <Text textStyle="TS6" className="regularL:font-medium w-1/2">
          {blog.title}
        </Text>
      </div>
      <div className="flex items-center gap-2 transition-opacity">
        <div className="flex flex-col regularL:flex-row gap-1">
          <Button
            variant="outline"
            size="icon"
            onClick={() => {
              props.onEdit();
            }}
            className="h-8 w-8"
          >
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
