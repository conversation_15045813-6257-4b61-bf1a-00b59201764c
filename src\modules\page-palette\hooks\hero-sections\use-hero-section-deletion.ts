import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useQueryClient } from "@tanstack/react-query";
import { deleteHeroSectionOnServerSide } from "../../services/hero-sections/deletion";

export default function useHeroSectionDeletion() {
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [deleteData, setDeleteData] = useState<{
    computerImage: string;
    mobileImage: string;
  } | null>(null);
  const queryClient = useQueryClient();
  const BASE_URL = process.env.BACKEND_ADDRESS || "";

  const onDelete = (data: { computerImage: string; mobileImage: string }) => {
    setDeleteData({
      computerImage: data.computerImage.replace(BASE_URL, ""),
      mobileImage: data.mobileImage.replace(BASE_URL, ""),
    });

    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
    setDeleteData(null); // Clear stored data
  };

  const deletePalette = async () => {
    if (!deleteData) return;

    setIsPending(true);

    try {
      await deleteHeroSectionOnServerSide(deleteData);
      queryClient.invalidateQueries({ queryKey: ["palettes"], exact: false });
      setAlertModalIsOpen(false);
    } catch (error) {
      const customError = error as CustomError;
    } finally {
      setIsPending(false);
    }
  };

  return {
    alertModalIsOpen,
    isPending,
    deletePalette,
    cancelDeletion,
    onDelete,
  };
}
