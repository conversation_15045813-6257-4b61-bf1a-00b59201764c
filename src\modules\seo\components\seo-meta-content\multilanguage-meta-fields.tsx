import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "next-intl";
import { Language } from "../../types/multilanguage-seo";

interface MultilanguageMetaFieldsProps {
  multilanguage: boolean;
  activeLanguage: Language;
  metaTitle: string;
  metaDescription: string;
  onTitleChange: (title: string) => void;
  onDescriptionChange: (description: string) => void;
}

export default function MultilanguageMetaFields({
  activeLanguage,
  multilanguage,
  metaTitle,
  metaDescription,
  onTitleChange,
  onDescriptionChange,
}: MultilanguageMetaFieldsProps) {
  const uploadContent = useTranslations(
    "shared.forms.upload.seoLabels.seoSettings"
  );

  const getFieldName = (fieldName: string) => {
    if (multilanguage && activeLanguage) {
      return `${fieldName}_${activeLanguage}`;
    }
    return fieldName;
  };

  return (
    <div className="flex flex-col space-y-5">
      <div>
        <label htmlFor={getFieldName("metaTitle")} className="text-gray">
          {uploadContent("MetaTitle")} {activeLanguage}
          {uploadContent("required")} :
        </label>
        <Input
          id="metaTitle"
          value={metaTitle}
          name={getFieldName("metaTitle")}
          onChange={(e) => onTitleChange(e.target.value)}
          className="mt-2"
          placeholder={uploadContent("MetaTitle")}
        />
      </div>

      <div>
        <label htmlFor={getFieldName("metaDescription")} className="text-gray">
          {uploadContent("MetaDescription")} {activeLanguage}
          {uploadContent("required")} :
        </label>
        <Textarea
          id="metaDescription"
          name={getFieldName("metaDescription")}
          value={metaDescription}
          onChange={(e) => onDescriptionChange(e.target.value)}
          className="mt-2 min-h-[100px]"
          placeholder={uploadContent("MetaDescription")}
        />
      </div>
    </div>
  );
}
