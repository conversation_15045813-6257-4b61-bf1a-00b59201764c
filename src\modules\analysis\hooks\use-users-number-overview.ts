import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import retrieveUsersNumberOverview from "../services/users-numbers-overview";

export default function useUsersNumberOverview() {
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<UsersNumberType>({
    queryKey: ["users-number-overview", user],
    queryFn: retrieveUsersNumberOverview,
    enabled: user !== null,
  });

  return {
    data,
    isLoading,
    isError,
  };
}
