import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Props {
  data: { name: string; id: string }[];
  name: string;
  selectedElementsId?: string[];
  handleCheck: (checked: boolean, checkedElementId: string) => void;
  disabled?: boolean;
}

export default function ElementsSelectionControlled({
  data,
  name,
  selectedElementsId = [],
  handleCheck,
  disabled = false,
}: Props) {
  return (
    <div className="w-full">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full"
            id="elements-selection-button"
            disabled={disabled}
          >
            {`${selectedElementsId.length} Produits sélectionnés`}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-full max-h-80 overflow-y-auto border">
          {data
            .sort(
              (a, b) =>
                Number(selectedElementsId.includes(b.id)) -
                Number(selectedElementsId.includes(a.id))
            )
            .map((element) => (
              <DropdownMenuCheckboxItem
                key={element.id}
                checked={selectedElementsId.includes(element.id)}
                onCheckedChange={(checked) => handleCheck(checked, element.id)}
              >
                {element.name}
              </DropdownMenuCheckboxItem>
            ))}
        </DropdownMenuContent>
      </DropdownMenu>
      {selectedElementsId.map((id) => (
        <input key={id} name={name} className="hidden" value={id} readOnly />
      ))}
    </div>
  );
}
