import {
  OrdersOverview,
  OrdersOverviewInResponse,
} from "@/modules/analysis/types/orders";

export function castToOrdersOverview(
  orders: OrdersOverviewInResponse
): OrdersOverview {
  return {
    currency: orders.currency,
    averageRevenueCurrentMonth: Number(orders.averageRevenueCurrentMonth),
    averageRevenueLastMonth: Number(orders.averageRevenueLastMonth),
    totalRevenueCurrentMonth: Number(orders.totalRevenueCurrentMonth),
    totalRevenueLastMonth: Number(orders.totalRevenueLastMonth),
    totalOrdersCurrentMonth: Number(orders.totalOrdersCurrentMonth),
    totalOrdersLastMonth: Number(orders.totalOrdersLastMonth),
  };
}
