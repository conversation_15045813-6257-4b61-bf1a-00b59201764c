import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";

import retrieveCategoriesSales from "../services/categories-sales-extraction";
import { CategoryTypeSalesType } from "../types/catalog";

export default function useCategoriesSales() {
  const { user } = useUser();
  const { data, isLoading, isError } = useQuery<CategoryTypeSalesType[]>({
    queryKey: ["categories", user],
    queryFn: retrieveCategoriesSales,
    enabled: user !== null,
  });

  return {
    categories: data,
    categoriesAreLoading: isLoading,
    categoriesError: isError,
  };
}
