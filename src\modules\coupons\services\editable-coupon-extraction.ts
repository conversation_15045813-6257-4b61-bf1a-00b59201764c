// modules/coupons/services/editable-coupon-extraction.ts
import { CouponType } from "../types/coupons"; // Adjust import path.
import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { CouponInResponseType } from "../types/coupons";
import { castToCouponType } from "../utils/data-management/type-casting/coupons";

export async function retrieveEditableCoupon(
  couponId: string
): Promise<CouponType> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`/coupons/${couponId}/dashboard`, headers);
    const coupon = castToCouponType(res.data as CouponInResponseType);

    if (!coupon) {
      throw new Error("Coupon not found.");
    }

    return coupon;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveEditableCoupon(couponId));
      if (!res) {
        throw new Error("Unauthorized");
      }
      return res;
    }

    throw error;
  }
}
