import { GET } from "@/lib/http-methods";
import extractJWTokens from "@auth/utils/jwt/extract-tokens";
import { refreshToken } from "@auth/services/refresh-token";
import { AxiosError } from "axios";
import { CategoryTypeSalesInResponseType } from "../types/catalog";
import { castToCategoryTypeSalesType } from "../utils/data-management/types-casting/catalog";

export default async function retrieveCategoriesSales() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET("/statistics/categories/types/details", headers);

    return (res.data as CategoryTypeSalesInResponseType[]).map((categoryType) =>
      castToCategoryTypeSalesType(categoryType)
    );
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveCategoriesSales);

      //unauthorized user error is already handled by the user hook
      if (!res) return [];

      return res;
    }

    return [];
  }
}
