import { HTMLAttributes, useState } from "react";
import Text from "@/styles/text-styles";
import { CouponType } from "../types/coupons";
import DiscountPercentage from "@assets/icons/discount-percentage";
import DiscountFixed from "@assets/icons/discount-fixed";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import FreeShipping from "@assets/icons/free-shipping";
import PriceDiscount from "@assets/icons/price-discount";
import CustomizableDialog from "@/components/customizable-dialog";
import CouponContainer from "./coupon-container";
import { Button } from "@/components/ui/button";

interface Props extends HTMLAttributes<"div"> {
  coupons: CouponType[];
  setElementsIds: (ids: string[]) => void;
}

export default function CouponsTable({ coupons, setElementsIds }: Props) {
  const t = useTranslations("CouponsManagement");
  const [allChecked, setAllChecked] = useState(false);
  const [checkedCoupons, setCheckedCoupons] = useState<string[]>([]);

  const [coupon, setCoupon] = useState<CouponType | null>(null);

  const handleDeletionCheck = (checked: boolean, id: string) => {
    let newCheckedCoupons: string[];
    if (checked) {
      newCheckedCoupons = [...checkedCoupons, id];
    } else {
      newCheckedCoupons = checkedCoupons.filter((couponId) => couponId !== id);
    }
    setCheckedCoupons(newCheckedCoupons);
    setElementsIds(newCheckedCoupons);
  };

  const handleBulkCheck = (checked: boolean) => {
    let newCheckedCoupons: string[];
    setAllChecked(checked);

    if (checked) {
      newCheckedCoupons = coupons.map((coupon) => coupon.id);
    } else {
      newCheckedCoupons = [];
    }
    setCheckedCoupons(newCheckedCoupons);
    setElementsIds(newCheckedCoupons);
  };

  const handleNameClick = (couponId: string) => {
    const isChecked = checkedCoupons.includes(couponId);
    handleDeletionCheck(!isChecked, couponId);
  };

  const handleOpenCouponDetails = (couponId: string) => {
    setCoupon(coupons.find((coupon) => coupon.id === couponId) || null);
  };

  const handleCloseCouponDetails = () => {
    setCoupon(null);
  };

  return (
    <Table>
      <TableHeader>
        <TableRow className="w-full bg-[#F7F9FC] hover:bg-[#F7F9FC]">
          <TableCell className="text-[#6F7182]">
            <Checkbox checked={allChecked} onCheckedChange={handleBulkCheck} />
          </TableCell>
          {t.raw("couponListColumns").map((label: string, idx: number) => (
            <TableCell key={idx} className="text-[#6F7182] py-5">
              <Text
                textStyle="TS7"
                className={cn({ "flex-1 flex justify-center": idx !== 0 })}
              >
                {label}
              </Text>
            </TableCell>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className="min-w-full text-[#6F7182]">
        {coupons.map((coupon) => (
          <TableRow key={coupon.id}>
            <TableCell className="">
              <Checkbox
                checked={checkedCoupons.includes(coupon.id)}
                onCheckedChange={(checked) =>
                  handleDeletionCheck(checked as boolean, coupon.id)
                }
              />
            </TableCell>
            {/* Name */}
            <TableCell
              className="font-medium flex items-center space-x-3  w-100 cursor-pointer"
              onClick={() => handleNameClick(coupon.id)}
            >
              {coupon?.discount?.type === "percentage" ? (
                <div className="  rounded-sm bg-blue border border-lightGray overflow-hidden flex items-center justify-center p-2">
                  <DiscountPercentage color="white" />
                </div>
              ) : coupon?.discount?.type === "amount" ? (
                <div className=" rounded-sm bg-gray border border-lightGray overflow-hidden flex items-center justify-center p-2">
                  <DiscountFixed color="white" />
                </div>
              ) : coupon?.discount?.type === "free" ? (
                <div className=" rounded-sm bg-gray border border-lightGray overflow-hidden flex items-center justify-center p-2">
                  <FreeShipping color="white" />
                </div>
              ) : (
                <div className=" rounded-sm bg-gray border border-lightGray overflow-hidden flex items-center justify-center p-2">
                  <PriceDiscount color="white" />
                </div>
              )}
              <div className="flex flex-col">
                <Text textStyle="TS6" className="text-black font-bold">
                  {coupon.code}
                </Text>
                <Text textStyle="TS7" className="text-gray">
                  {coupon.description}
                </Text>
              </div>
            </TableCell>
            <TableCell>
              <Text
                textStyle="TS6"
                className="flex-1 flex justify-center text-black"
              >
                {coupon.discount
                  ? coupon.discount.type == "percentage"
                    ? `${coupon.discount.value * 100} %`
                    : coupon.discount.type === "amount" &&
                      `${coupon.discount.value} TND`
                  : t("freeShipping")}
              </Text>
            </TableCell>
            <TableCell>
              <Text
                textStyle="TS6"
                className="flex-1 flex justify-center text-black"
              >
                {`${coupon.totalVoucherRedemptionsByUsers} fois`}
              </Text>
            </TableCell>
            {/* Status */}
            <TableCell className="flex justify-center">
              {coupon?.status === "active" ? (
                <div className="rounded-sm bg-light-green text-green flex items-center justify-center p-1 w-fit">
                  <Text textStyle="TS7">
                    {t("couponsInfo.couponStatus.active")}
                  </Text>
                </div>
              ) : coupon?.status === "expired" ? (
                <div className="rounded-sm bg-lightGray text-gray flex items-center justify-center p-1 w-fit">
                  <Text textStyle="TS7">
                    {t("couponsInfo.couponStatus.expired")}
                  </Text>
                </div>
              ) : (
                <div className="rounded-sm bg-light-red text-red flex items-center justify-center p-1 w-fit">
                  <Text textStyle="TS7">
                    {t("couponsInfo.couponStatus.inActive")}
                  </Text>
                </div>
              )}
            </TableCell>
            {/* Date */}
            <TableCell>
              <Text
                textStyle="TS6"
                className="flex-1 flex justify-center text-black"
              >
                {coupon.period.from && coupon.period.to
                  ? `${new Date(
                      coupon.period.from || ""
                    ).toLocaleDateString()} - ${new Date(
                      coupon.period.to || ""
                    ).toLocaleDateString()}`
                  : t("toujoursValide")}
              </Text>
            </TableCell>
            {/* Learn More */}
            <TableCell>
              <Button
                onClick={() => handleOpenCouponDetails(coupon.id)}
                className="w-fit bg-transparent shadow-none hover:bg-transparent"
                id="learn-more-button"
              >
                <Text
                  textStyle="TS6"
                  className="text-blue relative bg-transparent before:content-[''] before:absolute before:right-0 before:left-0 before:h-[1px] before:-bottom-1 before:bg-blue"
                >
                  {t("learnMore")}
                </Text>
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
      <CustomizableDialog
        title={t("couponsInfo.title")}
        setIsOpen={(open) => {
          if (!open) {
            setCoupon(null);
          }
        }}
        isOpen={coupon !== null}
        close={handleCloseCouponDetails}
      >
        {coupon && <CouponContainer coupon={coupon} />}
      </CustomizableDialog>
    </Table>
  );
}
