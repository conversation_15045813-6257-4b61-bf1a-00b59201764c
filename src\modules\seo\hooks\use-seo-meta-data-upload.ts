import { useState } from "react";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { CustomError } from "@/utils/custom-error";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import uploadSeoMetaDataToServerSide from "../services/seo-meta-data-upload";
import { useQueryClient } from "@tanstack/react-query";

export default function useSeoMetaDataUpload(
  getMetaContent: () => MultilanguageSeoContentPayload
) {
  const queryClient = useQueryClient();
  const [isPending, setIsPending] = useState(false);
  const { toast } = useToast();
  const t = useTranslations("warnings");
  const upload = useTranslations("shared.forms.upload.seoLabels");
  const [warning, setWarning] = useState("");

  async function submitMetaData() {
    setIsPending(true);

    try {
      const metaContentData = getMetaContent();

      setWarning("");

      const res = await uploadSeoMetaDataToServerSide({
        metaContent: metaContentData.content,
        name: "landing-page",
      });

      queryClient.invalidateQueries({ queryKey: ["palettes"], exact: false });

      toast({
        title: upload("successTitle"),
        description: upload("successDescription"),
      });

      return res.data;
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitMetaData,
    warning,
    isPending,
  };
}
