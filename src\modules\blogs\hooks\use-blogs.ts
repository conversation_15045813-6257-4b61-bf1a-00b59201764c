import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import useUrlParams from "@/hooks/urls-management/use-url-params";
import { BlogType } from "../types/blogs";
import { retrieveBlogsFromServerSide } from "../services/blogs-extraction";

interface Params {
  paginationAffectUrl?: boolean;
  limit?: number;
}

export default function useBlogs({
  limit,
  paginationAffectUrl = false,
}: Params) {
  const [searchedBlogs, setSearchedBlogs] = useState("");
  const [searchVersion, setSearchVersion] = useState(0);

  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination({ paginationAffectUrl });
  const { updateUrlParams, getParamFromUrl, removeParamFromUrl } =
    useUrlParams();

  const { data, isLoading, isError } = useQuery<{
    blogs: BlogType[];
    pagination: PaginationType;
  } | null>({
    queryKey: ["blogs", page, searchedBlogs],
    queryFn: () =>
      retrieveBlogsFromServerSide({
        page,
        limit,
        searchedBlogs,
      }),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    if (
      data &&
      data.pagination &&
      pagesNumber !== data.pagination?.totalPages
    ) {
      setPagesNumber(data.pagination.totalPages);
      setRecords(data.blogs.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  //get the searched products from the url
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchedBlogsInUrl = getParamFromUrl("search");

      if (searchedBlogsInUrl) setSearchedBlogs(searchedBlogsInUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  //update the url when the searched blogs change
  useEffect(() => {
    if (searchedBlogs !== "") {
      setPage(1);

      updateUrlParams("search", searchedBlogs);
    } else {
      const searchInUrl = getParamFromUrl("search");

      if (searchInUrl) removeParamFromUrl("search");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchVersion]);

  const searchBlogs = (text: string) => {
    setSearchedBlogs(text);
    setSearchVersion(searchVersion + 1);
  };

  return {
    blogs: data && data.blogs ? data?.blogs : [],
    blogsAreLoading: isLoading,
    blogsError: isError,
    setPage,
    page,
    records,
    pagesNumber,
    searchedBlogs,
    searchBlogs,
  };
}
