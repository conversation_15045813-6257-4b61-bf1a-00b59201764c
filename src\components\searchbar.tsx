import { Input } from "./ui/input";
import { useTranslations } from "next-intl";
import { Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { TextStyle } from "@/styles/text-styles";

interface Props {
  setSearchedContent: (text: string) => void;
  searchedContent: string;
  placeHolder?: string;
}

export default function Searchbar(props: Props) {
  const t = useTranslations("shared.searchBar");
  return (
    <div className="overflow-hidden L:pr-3 pr-2 flex items-center L:rounded-full rounded-xl border w-[250px]">
      <Input
        value={props.searchedContent}
        onChange={(e) => props.setSearchedContent(e.target.value)}
        className={cn(
          "bg-white L:pl-3 pl-2 py-1 border-none outline-none",
          TextStyle["TS6"]
        )}
        placeholder={props.placeHolder ? props.placeHolder : t("placeholder")}
      />
      <Search color="gray" />
    </div>
  );
}
