import { CustomError } from "@/utils/custom-error";

export function validateProductEditionData(formData: FormData): void {
  const name = formData.get("name") as string;
  const displayOrder = formData.get("displayOrder") as string;

  if (!name || name.trim() === "") {
    throw new CustomError("Product name is required!", 400);
  }

  const categoryIds = formData.getAll("categoryIds");
  if (!categoryIds || categoryIds.length === 0 || categoryIds[0] === "") {
    throw new CustomError("At least one category is required!", 400);
  }

  if (displayOrder && displayOrder.trim() !== "") {
    const orderNumber = parseInt(displayOrder, 10);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Display order must be a positive number!", 400);
    }
  }
}

export function validateProductEditionJsonData(
  submittedData: Record<string, any>
): void {
  if (!submittedData.name || submittedData.name.trim() === "") {
    throw new CustomError("Product name is required!", 400);
  }

  if (
    !submittedData.categoryIds ||
    (Array.isArray(submittedData.categoryIds) &&
      submittedData.categoryIds.length === 0) ||
    (typeof submittedData.categoryIds === "string" &&
      submittedData.categoryIds === "")
  ) {
    throw new CustomError("At least one category is required!", 400);
  }

  if (submittedData.displayOrder && submittedData.displayOrder.trim() !== "") {
    const orderNumber = parseInt(submittedData.displayOrder, 10);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Display order must be a positive number!", 400);
    }
  }
}
