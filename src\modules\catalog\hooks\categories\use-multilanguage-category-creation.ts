import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import uploadCategoryToServerSide from "../../services/categories/category-upload";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import cleanCategoryCreationFormData from "../../utils/form-data-cleaning/category-creation-form";
import { validateCategoryCreationData } from "../../validation/categories/validate-category-creation-data";

export default function useMultilanguageCategoryCreation(
  getMetaContent: () => MultilanguageSeoContentPayload
) {
  const t = useTranslations("warnings");
  const categoriesT = useTranslations("CategoriesManagement");

  const queryClient = useQueryClient();
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    if (language === categoriesT("languages.arabic"))
      setActiveLanguage("arabic");
    else if (language === categoriesT("languages.french"))
      setActiveLanguage("french");
    else if (language === categoriesT("languages.english"))
      setActiveLanguage("english");
  };

  async function submitCategory(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanCategoryCreationFormData(formData);
      validateCategoryCreationData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      await uploadCategoryToServerSide(filteredFormData);

      queryClient.invalidateQueries({
        queryKey: ["categories"],
        exact: false,
      });

      toast({
        title: t("upload.successTitle"),
        description: t("upload.successDescription"),
      });

      if (previousUrl && previousUrl.startsWith("/categories"))
        router.push(previousUrl);
      else router.push("/categories");
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({
          title: t("warning"),
          description: t("serverError"),
        });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({
            title: t("warning"),
            description: t("upload.missedData"),
          });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitCategory,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
  };
}
