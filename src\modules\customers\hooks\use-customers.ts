import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import { CustomerType } from "../types/customers";
import retrieveCustomers from "../services/customers-extraction";

export default function useCustomers(limit: number) {
  const { user } = useUser();

  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination();
  const [searchedCustomers, setSearchedCustomers] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("");

  const { data, isLoading, isError } = useQuery<{
    data: CustomerType[];
    pagination: PaginationType;
  }>({
    queryKey: [
      "customer",
      "customers",
      user,
      page,
      searchedCustomers,
      selectedFilter,
    ],
    queryFn: () =>
      retrieveCustomers(page, limit, searchedCustomers, selectedFilter),
    enabled: user !== null,
    placeholderData: (prev) => prev,
  });

  useEffect(() => {
    if (data?.pagination && pagesNumber !== data?.pagination?.totalPages)
      setPagesNumber(data.pagination.totalPages);

    if (data?.data) {
      setRecords(data.data.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    setPage(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchedCustomers]);

  const handleFilterChange = (value: string) => {
    setSelectedFilter(value);
  };

  return {
    customers: data?.data,
    customersAreLoading: isLoading,
    customersError: isError,
    setPage,
    selectedFilter,
    handleFilterChange,
    page,
    pagesNumber,
    records,
    setSearchedCustomers,
    searchedCustomers,
  };
}
