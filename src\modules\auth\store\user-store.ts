import { create } from "zustand";
import { UserDataType } from "../types";

type Store = {
  isLoading: boolean;
  user: UserDataType | null;
  setUser: (user: UserDataType | null) => void;
  setIsLoading: (isLoading: boolean) => void;
};

const useUserStore = create<Store>((set) => ({
  isLoading: true,
  user: null,
  setUser: (user) =>
    set((store) => ({
      user,
    })),
  setIsLoading: (isLoading) => set((store) => ({ isLoading })),
}));

export default useUserStore;
