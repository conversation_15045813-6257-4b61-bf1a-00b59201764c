import ImageUpload from "@/modules/catalog/components/images-management/image-upload";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import FormSubmission from "../../../form-submission";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import ListPicker from "@/components/list-picker";
import useEditableCategory from "@/modules/catalog/hooks/categories/use-editable-category";
import { CategoryType } from "@/modules/catalog/types/categories";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import { Input } from "@/components/ui/input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import LanguageTabs from "../../../brands/brand-upload/language-tabs";
import MultilanguageCategoryFormFields from "./multilanguage-category-form-fields";
import useMultilanguageCategoryEdition from "@/modules/catalog/hooks/categories/use-multilanguage-category-edition";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";

interface Props {
  parentCategories: CategoryType[];
  categoryParams: { id: string; slug: string };
  onSuccess: () => void;
  onCancel: () => void;
}

export default function CategoryEdition({
  categoryParams,
  parentCategories,
  onCancel,
}: Props) {
  const { id: categoryId, slug: categorySlug } = categoryParams;
  const [activeLanguage, setActiveLanguage] = useState("french");
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    element: category,
    elementIsLoading: categoryIsLoading,
    refetch,
  } = useEditableCategory({
    categorySlug,
    language: activeLanguage,
  });

  const {
    metaContent,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();

  const {
    formRef,
    submitCategory,
    warning,
    isPending,
    handleLanguageChange,
    formKey,
  } = useMultilanguageCategoryEdition(
    () => getMultilanguageMetaContent(),
    () => categoryId,
    () => {
      setIsUpdating(true);
      refetch().finally(() => {
        setIsUpdating(false);
      });
    },
    activeLanguage,
    setActiveLanguage
  );

  const { categories, categoriesAreLoading } = useCategories();
  const [choosedParentCategory, setchoosedParentCategory] =
    useState<null | CategoryType>(null);
  const [choosedSubParentCategory, setChoosedSubParentCategory] =
    useState<null | CategoryType>(null);
  const t = useTranslations("CategoriesManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  //setting the passed parent category
  useEffect(() => {
    if (parentCategories[0]) setchoosedParentCategory(parentCategories[0]);
    if (parentCategories.length > 1)
      setChoosedSubParentCategory(parentCategories[1]);
  }, [parentCategories]);

  return !(
    categoriesAreLoading ||
    (categoryIsLoading && !isUpdating) ||
    category === undefined ||
    categories === undefined
  ) ? (
    categories && category && (
      <FormSubmission
        submit={uploadContent("save")}
        cancel={uploadContent("cancel")}
        isPending={isPending}
        onCancel={onCancel}
        onSubmit={submitCategory}
        hideTopButtons
      >
        <form
          key={formKey}
          ref={formRef}
          className="flex flex-col extraXL:flex-row gap-4"
        >
          <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
            <div className="rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
              <Text textStyle="TS4" className="font-bold text-black">
                {t("subSubCategoryInfo")}
              </Text>
              <div className="text-red self-center">{warning}</div>
              <div className="flex flex-col space-y-5">
                {/* the top parent Type */}
                {
                  <Input
                    name="parentCategoryId"
                    className="hidden"
                    readOnly
                    value={
                      choosedSubParentCategory && choosedParentCategory
                        ? choosedSubParentCategory.id
                        : choosedParentCategory
                        ? choosedParentCategory.id
                        : "null"
                    }
                  />
                }

                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="categoryTypeId">
                    {`${uploadContent(
                      "categoryLabels.topParentategoryType"
                    )} ${uploadContent("required")}`}
                  </Label>
                  <ListPicker
                    data={categories}
                    onChange={(id) => {
                      const category = categories.find(
                        (category) => category.id === id
                      );

                      setchoosedParentCategory(category ? category : null);
                    }}
                    selectedElementId={
                      choosedParentCategory ? choosedParentCategory.id : ""
                    }
                  />
                </div>

                {choosedParentCategory && (
                  <div className="w-full flex flex-col space-y-2">
                    <Label htmlFor="categoryTypeId">
                      {`${uploadContent(
                        "categoryLabels.categoryType"
                      )} ${uploadContent("required")}`}
                    </Label>
                    <ListPicker
                      data={choosedParentCategory.subCategories}
                      onChange={(categoryId) => {
                        if (choosedParentCategory) {
                          const category =
                            choosedParentCategory.subCategories.find(
                              (category) => categoryId === category.id
                            );

                          setChoosedSubParentCategory(
                            category ? category : null
                          );
                        }
                      }}
                      selectedElementId={
                        choosedSubParentCategory
                          ? choosedSubParentCategory.id
                          : ""
                      }
                      pickedElementName={"categoryTypeId"}
                    />
                  </div>
                )}

                {/* Language Tabs and Multilanguage Fields */}
                <div className="w-full">
                  <LanguageTabs
                    options={[
                      { key: "arabic", value: t("languages.arabic") },
                      { key: "french", value: t("languages.french") },
                      { key: "english", value: t("languages.english") },
                    ]}
                    onSelect={handleLanguageChange}
                    selectedValue={activeLanguage}
                  />
                  <hr className="mb-4" />

                  <div
                    style={{
                      display: activeLanguage === "arabic" ? "block" : "none",
                    }}
                  >
                    <MultilanguageCategoryFormFields
                      key={`arabic-${category?.id}`}
                      multilanguage={true}
                      language="arabic"
                      initialName={category?.name || ""}
                      initialDescription={category?.description || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "french" ? "block" : "none",
                    }}
                  >
                    <MultilanguageCategoryFormFields
                      key={`french-${category?.id}`}
                      multilanguage={true}
                      language="french"
                      initialName={category?.name || ""}
                      initialDescription={category?.description || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "english" ? "block" : "none",
                    }}
                  >
                    <MultilanguageCategoryFormFields
                      key={`english-${category?.id}`}
                      multilanguage={true}
                      language="english"
                      initialName={category?.name || ""}
                      initialDescription={category?.description || ""}
                    />
                  </div>
                </div>

                {/* Display Order */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="displayOrder">
                    {`${uploadContent(
                      "categoryLabels.displayOrder"
                    )} ${uploadContent("optional")}`}
                  </Label>
                  <WarnInput
                    key={`displayOrder-${category?.id}`}
                    id="displayOrder"
                    name="displayOrder"
                    type="number"
                    onWheel={disableScrollOnNumberInput}
                    warning=""
                    placeholder={uploadContent("categoryLabels.displayOrder")}
                    value={
                      category.displayOrder === 0 ? "0" : category.displayOrder
                    }
                  />
                </div>

                {/* Image */}
                <div className="w-full flex flex-col space-y-2">
                  <Label>{uploadContent("categoryLabels.image")}</Label>
                  <ImageUpload name="image" defaultSrc={category.image} />
                </div>
              </div>
            </div>
          </div>
          <div className="basis-[30%] order-2 space-y-4">
            <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
              <MultilanguageSeoContent
                metaContent={metaContent}
                activeLanguage={activeLanguage as any}
                changeMetaTitle={handleMetaTitleChange}
                changeMetaDescription={handleMetaDescriptionChange}
                addNewKeyword={addNewKeyword}
                removeKeyword={removeKeyword}
              />
            </div>
          </div>
        </form>
      </FormSubmission>
    )
  ) : (
    <DashboardListsContainerSkeleton className="flex-1">
      {Array.from({ length: 6 }).map((_, idx) => (
        <div key={idx} className="w-full flex flex-col space-y-1">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-9 w-full max-w-[500px]" />
        </div>
      ))}
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
    </DashboardListsContainerSkeleton>
  );
}
