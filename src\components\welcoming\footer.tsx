import {
  faFacebookSquare,
  faInstagram,
  faLinkedinIn,
} from "@fortawesome/free-brands-svg-icons";
import {
  faCopyright,
  faEnvelope,
  faLocationDot,
  faPhone,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Text from "@/styles/text-styles";
import { IconProp } from "@fortawesome/fontawesome-svg-core";
import { NavButton } from "../buttons/nav-button";
import Image from "next/image";
import { useTranslations } from "next-intl";

const refrences = [
  "mailto:<EMAIL>",
  "https://www.tawer.tn#projectsSection",
  "https://www.tawer.tn#bookAMeetingSection",
];

export default function Footer() {
  const t = useTranslations("welcoming.footer");
  return (
    <div
      className={
        "pt-10 w-full pb-[50px] bg-white lg:px-[120px] px-[20px] flex flex-col items-center space-y-[80px] text-purple overflow-clip "
      }
    >
      <div className="flex lg:flex-row flex-col lg:space-x-[120px] lg:space-y-0 space-y-[56px] items-center">
        <Image
          src="/logos/tawer-logo.svg"
          alt="Company Logo"
          className="md:w-[363px] w-[257px] h-[102px] md:h-[72px]"
          width={363}
          height={102}
        />
        <div className="flex flex-col space-y-[10px]">
          <div className="flex space-x-[20px] cursor-default">
            <FontAwesomeIcon height={15} width={20} icon={faLocationDot} />
            <Text textStyle="TS4">Rte Bouzayene KM 9</Text>
          </div>
          <a
            href="tel:+21694184654"
            className="flex space-x-[20px] hover:opacity-80"
            id="footer-phone-link"
          >
            <FontAwesomeIcon height={15} width={20} icon={faPhone} />
            <Text textStyle="TS4">+216 25 50 50 61</Text>
          </a>
          <a
            href="mailto:<EMAIL>"
            className="flex space-x-[20px] hover:opacity-80"
            id="footer-email-link"
          >
            <FontAwesomeIcon height={15} width={20} icon={faEnvelope} />
            <Text textStyle="TS4"><EMAIL></Text>
          </a>
          <div className="flex space-x-[20px]">
            <a
              href="https://www.facebook.com/profile.php?id=61556992417564&mibextid=ZbWKwL"
              aria-label="Facebook link"
              target="_blank"
              className="hover:opacity-80"
              id="footer-facebook-link"
            >
              <FontAwesomeIcon size="lg" icon={faFacebookSquare as IconProp} />
            </a>
            <a
              href="https://www.instagram.com/tawer.digital.services?igsh=MTE0aTFxeDk1Nm81dg=="
              aria-label="Instagram link"
              target="_blank"
              className="hover:opacity-80"
              id="footer-instagram-link"
            >
              <FontAwesomeIcon size="lg" icon={faInstagram as IconProp} />
            </a>
            <a
              href="https://www.linkedin.com/company/tawer-digital-services/"
              target="_blank"
              className="hover:opacity-80"
              aria-label="LinkedIn link"
              id="footer-linkedin-link"
            >
              <FontAwesomeIcon size="lg" icon={faLinkedinIn as IconProp} />
            </a>
          </div>
        </div>
      </div>
      <div className="w-full flex lg:flex-row flex-col lg:justify-between lg:items-start lg:space-y-0 items-center space-y-[11px]">
        <div className="flex md:space-x-[20px] space-x-[10px] items-center">
          {t.raw("elements").map((footerElement: string, index: number) =>
            index == refrences.length - 1 ? (
              <NavButton
                link={refrences[index]}
                key={index}
                className="uppercase hover:opacity-80 text-center text-purple"
                id={`footer-nav-button-${index}`}
              >
                {footerElement}
              </NavButton>
            ) : (
              <NavButton
                key={index}
                link={refrences[index]}
                className="uppercase hover:opacity-80 text-center text-purple"
                id={`footer-nav-button-${index}`}
              >
                {footerElement}
              </NavButton>
            )
          )}
        </div>
        <Text textStyle="TS4" className="flex">
          Copyright
          <span className="mx-1">
            <FontAwesomeIcon icon={faCopyright} />
          </span>
          Tawer Digital Services
        </Text>
      </div>
    </div>
  );
}
