import useTimer from "@auth/hooks/use-timer";
import { verifyResetPasswordCode } from "@/modules/auth/services/reset-password/code-verification";
import { useState } from "react";
import { useTranslations } from "next-intl";

export default function useCodeVerification() {
  const [code, setCode] = useState("");
  const { displayedTimer, startTimer } = useTimer(600);
  const t = useTranslations("auth");

  async function verifyCode(
    email: string,
    code: string
  ): Promise<{ message: string; verified: boolean }> {
    try {
      const res = await verifyResetPasswordCode({ email, code });

      return {
        message: res.ok ? "" : t("warnings.invalidCode"),
        verified: res.ok,
      };
    } catch (error) {
      return { message: "", verified: false };
    }
  }

  return {
    verifyCode,
    displayedTimer,
    startTimer,
    code,
    setCode,
  };
}
