import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import MultilanguageMetaFields from "./multilanguage-meta-fields";
import { Language } from "../../types/multilanguage-seo";

interface SeoSettingsProps {
  metaTitle: string;
  metaDescription: string;
  activeLanguage: Language;
  changeMetaTitle: (title: string) => void;
  changeMetaDescription: (description: string) => void;
}

export default function SeoMetaContent({
  metaTitle,
  metaDescription,
  activeLanguage,
  changeMetaTitle,
  changeMetaDescription,
}: SeoSettingsProps) {
  const uploadContent = useTranslations(
    "shared.forms.upload.seoLabels.seoSettings"
  );

  return (
    <div className="space-y-7">
      <Text textStyle="TS5" className="font-bold text-black">
        {uploadContent("title")}
      </Text>

      <div className="w-full">
        <div
          style={{
            display: activeLanguage === "arabic" ? "block" : "none",
          }}
        >
          <MultilanguageMetaFields
            multilanguage={true}
            activeLanguage={"arabic"}
            metaTitle={metaTitle}
            metaDescription={metaDescription}
            onTitleChange={changeMetaTitle}
            onDescriptionChange={changeMetaDescription}
          />
        </div>
        <div
          style={{
            display: activeLanguage === "french" ? "block" : "none",
          }}
        >
          <MultilanguageMetaFields
            multilanguage={true}
            activeLanguage={"french"}
            metaTitle={metaTitle}
            metaDescription={metaDescription}
            onTitleChange={changeMetaTitle}
            onDescriptionChange={changeMetaDescription}
          />
        </div>

        <div
          style={{
            display: activeLanguage === "english" ? "block" : "none",
          }}
        >
          <MultilanguageMetaFields
            multilanguage={true}
            activeLanguage={"english"}
            metaTitle={metaTitle}
            metaDescription={metaDescription}
            onTitleChange={changeMetaTitle}
            onDescriptionChange={changeMetaDescription}
          />
        </div>
      </div>
    </div>
  );
}
